# 🚀 页面构建器 - 模块化单页面开发工具

> 专为高效单页面开发而生，实现"所见即所得"的开发体验

[![Node.js](https://img.shields.io/badge/Node.js-18%2B-green)](https://nodejs.org/)
[![Vite](https://img.shields.io/badge/Vite-5.x-purple)](https://vitejs.dev/)
[![SCSS](https://img.shields.io/badge/SCSS-1.69%2B-pink)](https://sass-lang.com/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

## ✨ 项目特色

### 🎯 核心价值

- **所见即所得**: 开发时完整预览最终页面效果（header+main+footer）
- **实时热更新**: 修改 main.html 立即在预览页面自动刷新，无需手动操作
- **模块化开发**: 专注 main 区域开发，头部底部保持固定
- **智能文件管理**: 保留源文件结构，同时输出完整 HTML
- **UTF-8 完美支持**: 中文内容零乱码问题
- **现代工具链**: Vite + SCSS + ES6，极致开发体验

### 📋 功能特点

✅ 三部分分离架构（Header、Main、Footer）

✅ 完整页面实时预览

✅ **🔥 智能热更新**：0.5 秒检测，main.html 修改自动同步到预览页面

✅ SCSS 模块化样式管理

✅ **📁 智能资源管理**：支持图片、视频、文档等资源文件自动路径转换

✅ **🖼️ 视频图片分离**：支持视频和图片使用不同的 CDN 基础路径

✅ **⚡ 自动懒加载**：为 main 部分的图片自动添加 loading="lazy"属性

✅ **🎨 全面路径支持**：HTML 属性(src/href/srcset/poster)和 CSS(url())路径转换

✅ CDN 资源独立配置

✅ 自动构建合并为单页 HTML

✅ **📦 智能脚本压缩**：保持原始变量名和注释格式

✅ 响应式设计支持

✅ 开发工具栏快捷操作

✅ **🔍 高效预览生成**：支持自动生成多种预览布局

## 🏗️ 项目架构

```
📦 页面构建器
├── 📁 src/                    # 开发源码目录
│   ├── 📁 templates/          # 模板文件
│   │   ├── base.html          # 基础页面模板
│   │   ├── header.html        # 公共头部
│   │   ├── footer.html        # 公共底部
│   │   └── page-preview.html  # 页面预览模板（含热更新）
│   ├── 📁 pages/              # 页面源文件
│   │   └── 📁 [页面名]/
│   │       ├── main.html      # 主要内容（热更新监听）
│   │       ├── style.scss     # 页面样式
│   │       ├── script.js      # 页面脚本
│   │       ├── preview.html   # 完整预览（含热更新）
│   │       └── 📁 assets/     # 页面资源文件（可选）
│   │           ├── images/    # 图片资源
│   │           ├── videos/    # 视频资源
│   │           └── files/     # 其他文档资源
│   ├── 📁 shared/             # 共享资源
│   │   ├── 📁 scss/           # 样式文件
│   │   │   ├── _variables.scss # 变量定义
│   │   │   ├── _mixins.scss   # 混合器
│   │   │   ├── _base.scss     # 基础样式
│   │   │   ├── _header.scss   # 头部样式
│   │   │   └── _footer.scss   # 底部样式
│   │   └── 📁 js/
│   │       └── common.js      # 公共脚本
│   ├── 📁 config/
│   │   └── pages.json         # 页面配置
│   ├── 📁 assets/             # 全局资源目录
│   │   └── 📁 favicons/       # 网站图标
│   └── index.html             # 开发预览首页
├── 📁 dist/                   # 构建输出目录
├── 📁 scripts/                # 工具脚本
│   └── create-page.js         # 创建新页面脚本
├── 📁 shared/                 # 全局共享资源
└── package.json
```

## 🚀 快速开始

### 环境要求

- Node.js 18+
- npm 或 yarn

### 安装启动

```bash
# 1. 克隆项目
git clone <repository-url>
cd page-builder

# 2. 安装依赖
npm install

# 3. 启动开发服务器
npm run dev

# 4. 访问开发预览
# 浏览器自动打开 http://localhost:3000/
```

## 💻 开发工作流

### 🎯 完整工作流程

```mermaid
graph TD
    A[创建新页面] --> B[编辑main.html]
    B --> C[实时热更新预览]
    C --> D[编写style.scss]
    D --> E[开发script.js]
    E --> F[添加assets资源]
    F --> G[完整页面预览]
    G --> H{满意效果?}
    H -->|否| B
    H -->|是| I[构建页面]
    I --> J[交付HTML文件]
```

### 1️⃣ 创建新页面

```bash
npm run new-page <页面名> [页面标题]

# 示例
npm run new-page product-page "产品介绍页"
```

自动创建完整目录结构，包含热更新支持。

### 2️⃣ 开发阶段

**访问开发预览**: http://localhost:3000/

在项目首页可以看到所有页面：

- 🚀 **完整预览**: 查看包含 header+main+footer 的完整页面效果
- 📝 **预览 Main**: 只查看 main 区域内容
- 🔨 **构建页面**: 生成最终 HTML 文件

**编辑文件**:

```
src/pages/[页面名]/
├── main.html     # 编辑主要内容（热更新监听）
├── style.scss    # 编写页面样式
├── script.js     # 开发交互功能
└── assets/       # 资源文件（自动路径转换）
    ├── images/   # 图片资源
    ├── videos/   # 视频资源
    └── files/    # 其他文档
```

### 3️⃣ 实时预览与热更新

- **模块预览**: `http://localhost:3000/pages/[页面名]/main.html`
- **完整预览**: `http://localhost:3000/pages/[页面名]/preview.html`
- **🔥 热更新**: 修改 main.html 后，预览页面自动在 0.5 秒内刷新
- **开发工具栏**: 提供快捷编辑和构建按钮

**热更新工作原理**:

- ✅ 智能轮询检测：每 0.5 秒检查 main.html 文件变化
- ✅ 内容哈希比较：精确识别文件内容更新
- ✅ 无缝刷新：保持滚动位置，重新执行脚本
- ✅ 状态提示：显示更新通知和错误处理
- ✅ 开发友好：启动即可用，无需额外配置

### 4️⃣ 资源文件管理

**Assets 目录支持**:

```html
<!-- 开发时使用相对路径 -->
<img src="assets/images/banner.jpg" alt="横幅" />
<a href="assets/files/manual.pdf">下载手册</a>
<video src="assets/videos/demo.mp4" poster="assets/images/poster.jpg"></video>

<!-- 构建时自动转换为正确路径 -->
<img src="pages/product-page/assets/images/banner.jpg" alt="横幅" />
<a href="pages/product-page/assets/files/manual.pdf">下载手册</a>
<video src="pages/product-page/assets/videos/demo.mp4" poster="pages/product-page/assets/images/poster.jpg"></video>
```

### 5️⃣ 构建交付

```bash
npm run build [页面id]
```

- **只构建某个页面**：

  ```bash
  npm run build test-page
  ```

  只会生成 `dist/当天日期-deleted-files/`，并自动删除之前所有 `*-deleted-files` 目录（`deleted-files` 来自页面 output 字段 `deleted-files.html`）。

- **构建全部页面**：
  ```bash
  npm run build
  ```
  会为每个页面都清理历史目录，只保留当天最新的。

#### ⚠️ 构建目录清理与命名规则

- 构建输出目录格式为：`dist/日期-页面output名/`，如 `dist/2025_07_16-deleted-files/`。
- 每次构建前，自动删除 dist 下所有历史的 `*-页面output名` 目录，只保留本次构建的最新目录。
- “页面 output 名”取自页面配置的 `output` 字段（去掉 `.html` 后缀），**不是页面 id**。
- 例如：
  - `test-page` 的 output 是 `deleted-files.html`，则只会清理 `dist/日期-deleted-files/` 相关目录。
  - `demo-page` 的 output 是 `Canon-Camera-Repair.html`，则只会清理 `dist/日期-Canon-Camera-Repair/` 相关目录。
- 这样可确保每个页面的历史构建目录不会混淆，且始终只保留最新版本，目录结构清晰。

构建完成后：

- ✅ `dist/日期-页面output名/` - 完整的单页 HTML 文件及相关源文件（交付版本）
- ✅ `src/pages/[页面id]/` - 保留的模块化源文件（便于维护）
- ✅ 资源文件路径自动转换为生产环境路径

## 🎨 样式开发

### SCSS 架构

```scss
// 页面样式文件示例
@import "../../shared/scss/variables";
@import "../../shared/scss/mixins";

.main-content {
  .hero {
    @include gradient-bg();
    padding: $spacing-xxl 0;

    h1 {
      font-size: $font-size-xxxl;
      color: $text-color;
    }
  }
}
```

### 可用变量和混合器

```scss
// 颜色变量
$primary-color, $secondary-color, $accent-color
$text-color, $light-text-color, $bg-color

// 间距变量
$spacing-xs, $spacing-sm, $spacing-md, $spacing-lg

// 响应式混合器
@include respond-to('mobile')
@include respond-to('tablet')

// 按钮混合器
@include btn-base
@include btn-primary
```

## 🔧 配置管理

### 页面配置 (src/config/pages.json)

```json
{
  "pages": [
    {
      "id": "demo-page",
      "title": "演示页面",
      "description": "页面描述",
      "output": "demo-page.html",
      "cdnStyles": ["https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"],
      "cdnScripts": ["https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"],
      "assetsConfig": {
        "baseUrl": "https://images.example.com/assets/demo-page/",
        "videoBaseUrl": "https://videos.example.com/assets/demo-page/",
        "supportedExtensions": [
          ".jpg",
          ".jpeg",
          ".png",
          ".gif",
          ".svg",
          ".webp",
          ".mp4",
          ".webm",
          ".ogg",
          ".avi",
          ".mov",
          ".pdf",
          ".doc",
          ".docx",
          ".zip",
          ".rar"
        ],
        "videoExtensions": [".mp4", ".webm", ".ogg", ".avi", ".mov"]
      }
    }
  ]
}
```

### CDN 资源配置

- **cdnStyles**: 外部 CSS 文件数组
- **cdnScripts**: 外部 JS 文件数组
- **baseUrl**: 图片和一般资源的 CDN 基础路径
- **videoBaseUrl**: 视频资源的 CDN 基础路径（可与图片分离）
- 自动处理加载顺序和依赖

## 📱 开发体验特色

### 🚀 完整页面预览

- **开发预览条**: 固定在页面顶部，提供快捷操作
- **实时加载**: 动态组合 header+main+footer
- **所见即所得**: 开发时看到的就是最终效果
- **预览生成器**: 支持通过 `preview-generator.js` 生成不同布局的预览

### ⚡ 智能热更新系统

**核心特性**:

- 🔄 **0.5 秒响应**: 修改 main.html 后自动检测并刷新预览页面
- 🎯 **精确检测**: 基于内容哈希比较，避免无效刷新
- 💡 **智能提示**: 显示"页面已更新"通知和加载状态
- 🛡️ **错误处理**: 网络错误、文件丢失等异常情况的完善处理
- 📍 **状态保持**: 刷新后恢复滚动位置，重新执行脚本

**开发日志**:

```
🔥 热更新已启用，检查间隔: 0.5秒
📥 检测到文件变化，正在重新加载...
✅ 页面内容已更新
```

**技术实现**:

- 客户端轮询机制（避免 WebSocket 兼容性问题）
- 内容变化智能检测算法
- DOM 无缝更新和脚本重新执行
- 错误恢复和重试机制

### 🎯 智能工具栏

页面预览时提供：

- 📝 **编辑 Main**: 快速定位到 main.html
- 🎨 **编辑样式**: 快速定位到 style.scss
- ⚡ **编辑脚本**: 快速定位到 script.js
- 🔨 **构建**: 一键生成最终 HTML
- 📱 **响应式预览**: 快速切换不同设备预览模式

## 📦 构建特性

### 双重输出管理

**开发文件** (保留):

```
src/pages/demo-page/
├── main.html      # 模块化内容
├── style.scss     # 源样式文件
├── script.js      # 源脚本文件
└── assets/        # 资源文件
    ├── images/
    ├── videos/
    └── files/
```

**构建文件** (交付):

```
dist/
└── demo-page.html # 完整单页HTML
                   # 包含所有样式和脚本
                   # 资源路径已转换
```

### 自动优化

- ✅ SCSS 编译和压缩
- ✅ JavaScript 压缩
- ✅ UTF-8 编码保证
- ✅ CDN 资源整合
- ✅ Assets 路径自动转换
- ✅ 图片懒加载优化

## 🛠️ 脚本命令

```bash
# 开发命令
npm run dev          # 启动开发服务器（含热更新）
npm run dev:host     # 启动并允许外部访问

# 构建命令
npm run build        # 构建所有页面
npm run build:single # 构建单个页面

# 工具命令
npm run new-page     # 创建新页面（自动包含热更新）
npm run clean        # 清理构建文件
```

## 🛠️ 资源文件批量重命名工具

为保证所有页面资源文件命名规范，项目内置了批量重命名脚本：

### 使用方法

```bash
npm run rename-assets
```

- 会自动递归处理 `src/pages` 下所有页面的 `assets` 目录及其所有子目录下的所有文件。
- 支持图片、视频、文档等所有类型文件。

### 命名规则

1. 所有字母小写。
2. 单词之间用连字符（-）分隔。
3. 除连字符外，去除所有特殊字符（如空格、&、#、@、%、中文、标点等）。
4. 文件扩展名保持不变。

**示例：**

- `Partial  Incomplete File.png` → `partial-incomplete-file.png`
- `Voice Recorder & Phone Mic.jpg` → `voice-recorder-phone-mic.jpg`
- `Advanced.svg` → `advanced.svg`

### 注意事项

- 已存在重名文件时自动跳过并提示。
- 对于仅大小写不同的文件名（如 `Advanced.svg` → `advanced.svg`），会自动处理为全小写。
- 建议在资源整理或上线前执行一次，确保所有文件名规范统一。

## 🌟 最佳实践

### 📝 内容组织

```html
<!-- main.html 内容结构建议 -->
<main class="main-content">
  <section class="hero">
    <!-- 英雄区域 -->
    <img src="assets/images/hero-banner.jpg" alt="横幅" loading="lazy" />
  </section>

  <section class="features">
    <!-- 特性展示 -->
  </section>

  <section class="cta">
    <!-- 行动号召 -->
    <a href="assets/files/brochure.pdf" download>下载资料</a>
  </section>
</main>
```

### 🎨 样式规范

```scss
// 使用BEM命名规范
.main-content {
  .hero {
    &__title {
    }
    &__subtitle {
    }
    &--primary {
    }
  }
}

// 利用响应式混合器
@include respond-to("mobile") {
  // 移动端样式
}
```

### ⚡ 性能优化

- 合理使用 CDN 资源
- 避免内联大量样式
- 图片使用适当格式和大小
- 脚本使用异步加载
- 利用自动懒加载提高页面性能
- 利用热更新提高开发效率

### 🔥 热更新最佳实践

**开发建议**:

- 保持 preview.html 页面打开，专注编辑 main.html
- 利用 0.5 秒响应时间快速迭代
- 大幅修改时可暂时切换到 main.html 直接预览
- 注意控制台日志了解热更新状态

**性能考虑**:

- 0.5 秒检查间隔在开发环境下性能友好
- 生产环境自动禁用热更新功能
- 轮询机制避免 WebSocket 连接问题

## 🔍 常见问题

### Q: 热更新不生效怎么办？

A: 检查以下几点：

1. 确保在 preview.html 页面（不是 main.html）
2. 查看控制台是否有"🔥 热更新已启用"提示
3. 检查网络连接，热更新依赖 HTTP 请求
4. 尝试手动刷新页面重新启动热更新

### Q: 如何处理中文编码问题？

A: 项目已完美支持 UTF-8 编码，确保所有中文内容正常显示。

### Q: Assets 资源文件如何使用？

A: 在 main.html 中使用相对路径`assets/images/xxx.jpg`，构建时自动转换为正确的相对路径。

### Q: 如何在页面间共享组件？

A: 在 `src/shared/` 或项目根目录下的 `shared/` 目录中创建共享资源，通过模板或样式引入。

### Q: 构建后还能继续开发吗？

A: 是的！源文件保留在 `src/pages/` 中，随时可以继续开发和重新构建。

### Q: 如何自定义头部和底部？

A: 编辑 `src/templates/header.html` 和 `src/templates/footer.html`。

### Q: 支持 TypeScript 吗？

A: 目前支持 ES6+ JavaScript，TypeScript 支持可通过配置 Vite 添加。

### Q: 热更新影响性能吗？

A: 开发环境下 0.5 秒轮询对性能影响极小，生产环境自动禁用。

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

---

<div align="center">
<p>💻 用 ❤️ 打造</p>
</div>
