{
  "html.validate.scripts": true, // 启用脚本验证
  "html.validate.styles": true, // 启用样式验证
  "html.problems.enabled": true, // 启用HTML问题检测
  "html.hover.documentation": true, // 启用悬停文档
  "emmet.includeLanguages": {
    "html": "html",
    "scss": "scss"
  },
  "files.associations": {
    "*.html": "html",
    "*.scss": "scss"
  },
  // 保持自定义模板标签配置
  "html.customData": [
    {
      "version": 1.1,
      "tags": [
        {
          "name": "template",
          "description": "Custom template tag"
        }
      ],
      "globalAttributes": [],
      "valueSets": []
    }
  ]
}
