{
  "html.validate.scripts": false,
  "html.validate.styles": false,
  "emmet.includeLanguages": {
    "html": "html"
  },
  "html.customData": [
    {
      "version": 1.1,
      "tags": [
        {
          "name": "template",
          "description": "Custom template tag"
        }
      ],
      "globalAttributes": [],
      "valueSets": []
    }
  ],
  "files.associations": {
    "*.html": "html"
  },
  // 禁用特定的HTML警告
  "html.problems.enabled": false,
  // 或者更精确地只禁用模板相关警告
  "html.hover.documentation": false,
  "livePreview.defaultPreviewPath": "/temp.html",
  "liveServer.settings.port": 5501
}
