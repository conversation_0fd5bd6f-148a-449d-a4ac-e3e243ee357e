// scripts/rename-assets.js
// 批量重命名 src/pages 下所有页面 assets 目录及其子目录下的所有文件
// 规则：全部小写，单词用连字符分隔，去除除连字符外的所有特殊字符，保留扩展名

const fs = require("fs");
const path = require("path");

// 生成规范文件名
function normalizeFileName(filename) {
  const ext = path.extname(filename);
  let name = path.basename(filename, ext);
  // 替换所有非字母数字为连字符
  name = name.replace(/[^a-zA-Z0-9]+/g, "-");
  // 合并多余的连字符
  name = name.replace(/-+/g, "-");
  // 去除首尾连字符
  name = name.replace(/^-+|-+$/g, "");
  // 全部小写
  name = name.toLowerCase();
  return name + ext.toLowerCase();
}

// 递归处理目录
function renameFilesInDir(dir) {
  const items = fs.readdirSync(dir, { withFileTypes: true });
  for (const item of items) {
    const fullPath = path.join(dir, item.name);
    if (item.isDirectory()) {
      renameFilesInDir(fullPath);
    } else if (item.isFile()) {
      const newName = normalizeFileName(item.name);
      if (newName !== item.name) {
        const newPath = path.join(dir, newName);
        if (fs.existsSync(newPath) && newPath.toLowerCase() !== fullPath.toLowerCase()) {
          console.warn(`⚠️ 跳过重名文件: ${newPath}`);
        } else if (newPath.toLowerCase() === fullPath.toLowerCase()) {
          // 仅大小写不同，需用临时名中转
          const tmpPath = path.join(dir, "__tmp__" + newName);
          fs.renameSync(fullPath, tmpPath);
          fs.renameSync(tmpPath, newPath);
          console.log(`✅ 重命名(大小写): ${item.name} → ${newName}`);
        } else {
          fs.renameSync(fullPath, newPath);
          console.log(`✅ 重命名: ${item.name} → ${newName}`);
        }
      }
    }
  }
}

// 主入口
function main() {
  const pagesRoot = path.join(__dirname, "../src/pages");
  if (!fs.existsSync(pagesRoot)) {
    console.error("❌ 未找到 src/pages 目录");
    process.exit(1);
  }
  const pageDirs = fs.readdirSync(pagesRoot, { withFileTypes: true }).filter((d) => d.isDirectory());
  for (const pageDir of pageDirs) {
    const assetsDir = path.join(pagesRoot, pageDir.name, "assets");
    if (fs.existsSync(assetsDir)) {
      console.log(`
📁 处理页面: ${pageDir.name}/assets`);
      renameFilesInDir(assetsDir);
    }
  }
  console.log("\n🎉 批量重命名完成！");
}

if (require.main === module) {
  main();
}
