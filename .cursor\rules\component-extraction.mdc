---
alwaysApply: false
---
请帮我将选中的代码片段抽离封装成一个可复用的前端组件，并完成以下任务：

## 任务要求：
1. **组件抽离**：从选中的代码片段中提取出[组件名称]组件的完整代码
2. **代码分离**：将组件分离为HTML结构、SCSS样式、JavaScript功能三个部分
3. **测试页面**：生成一个独立的HTML测试页面用于验证组件功能

## 技术规范（必须严格遵守）：
- **布局与组件**: 统一使用 Bootstrap v4.6.2 进行所有响应式布局和基础组件的构建
- **轮播与滑块**: 统一使用 Swiper.js v7 来实现所有的滑块和轮播效果  
- **脚本库**: 统一使用 jQuery 作为主要且默认的脚本库

## 输出要求：
1. **HTML结构**：提供干净的组件HTML结构，去除页面特定的样式和ID
2. **SCSS样式**：使用嵌套语法，包含响应式断点，变量化可配置参数
3. **JavaScript代码**：使用jQuery语法，保持简洁实用
4. **完整测试页面**：生成包含所有依赖的独立HTML文件，确保功能完整可测试
5. **通用化处理**：将所有文案、类名、ID等改为通用性强且简洁的命名，避免业务特定内容

## 特别注意：
- 确保Bootstrap组件（如手风琴）的`data-parent`属性正确设置，实现互斥展开
- Swiper配置要符合v7版本语法
- 所有交互功能必须使用jQuery实现
- 测试页面要包含完整的CDN引用和示例数据
- 组件要支持响应式布局和移动端适配
- **文案通用化**：将具体的业务文案替换为"标题1"、"内容描述"等通用占位文本
- **命名简洁化**：使用简洁明了的class名称和变量名，便于理解和复用

请按照以上要求完成[组件名称]组件的完整抽离和封装工作。
