# 🌟 可爱的 Favicon 图标库

这个目录包含了为你的项目精心挑选的可爱 emoji favicon 图标。

## 📁 可用图标

| 图标文件              | 图标预览 | 推荐用途          | 描述                      |
| --------------------- | -------- | ----------------- | ------------------------- |
| `eye-emoji.svg`       | 👀       | 预览页面          | 眼睛图标，表示"查看/预览" |
| `smiling-face.svg`    | 😊       | 主页面/默认页面   | 微笑脸，表示友好和欢迎    |
| `star-struck.svg`     | 🤩       | 演示页面/特色页面 | 星眼图标，表示惊艳和兴奋  |
| `sparkling-heart.svg` | 💖       | 测试页面/其他页面 | 闪亮心形，表示喜爱和特别  |

## 🔧 如何配置

### 方法 1：在 pages.json 中配置（推荐）

在 `src/config/pages.json` 中为每个页面添加 `favicon` 字段：

```json
{
  "pages": [
    {
      "id": "your-page",
      "title": "页面标题",
      "description": "页面描述",
      "favicon": "./assets/favicons/star-struck.svg",
      "output": "output-filename.html"
    }
  ]
}
```

### 方法 2：直接在 HTML 中添加

在 HTML 的 `<head>` 部分添加：

```html
<!-- 可爱的Favicon 🌟 -->
<link rel="icon" type="image/svg+xml" href="/src/assets/favicons/eye-emoji.svg" />
<link rel="icon" type="image/png" href="/src/assets/favicons/eye-emoji.svg" />
<link rel="apple-touch-icon" href="/src/assets/favicons/eye-emoji.svg" />
```

## 🎨 自定义图标

如果你想添加更多图标：

1. 将 SVG 图标文件放在 `src/assets/favicons/` 目录下
2. 确保文件名有意义且易于识别
3. 在 `pages.json` 中引用新图标路径
4. 推荐使用 emoji SVG 图标以保持一致的风格

## 📱 浏览器兼容性

这些图标已配置为支持：

- ✅ 现代浏览器（Chrome, Firefox, Safari, Edge）
- ✅ 移动端浏览器
- ✅ Apple Touch Icon（用于添加到主屏幕）
- ✅ PWA 应用

## 🎯 使用建议

- **预览页面**：使用 👀 `eye-emoji.svg`
- **主要页面**：使用 😊 `smiling-face.svg`
- **特色/演示页面**：使用 🤩 `star-struck.svg`
- **测试/实验页面**：使用 💖 `sparkling-heart.svg`

## 📝 注意事项

- 所有图标都来自开源的 [Twemoji](https://twemoji.twitter.com/) 项目
- 图标使用 CC-BY 4.0 许可证，可自由使用
- SVG 格式确保在所有分辨率下都有清晰的显示效果
- 预览页面会自动使用眼睛图标，最终生成的页面使用配置中指定的图标

---

💡 **提示**：选择合适的 favicon 能够提升用户体验，让你的页面在浏览器标签中更容易识别！
