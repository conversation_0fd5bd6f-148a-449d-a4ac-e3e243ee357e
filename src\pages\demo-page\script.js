$(() => {
  // 文字轮播
  const bannerTextSwiper = new Swiper("#banner-text-swiper", {
    slidesPerView: 1,
    spaceBetween: 10,
    loop: true,
    direction: "vertical",
    allowTouchMove: false, // 禁止手动滑动
    autoplay: {
      delay: 2500,
      disableOnInteraction: false,
    },
  });
  if (window.innerWidth > 1280) {
    $(".document-wrapper .document-card").on("mouseenter", function () {
      $(this).addClass("active").siblings().removeClass("active");
    });
    $(".archive-wrapper .archive-card").on("mouseenter", function () {
      $(this).addClass("active").siblings().removeClass("active");
    });
  } else {
    $(".document-wrapper .document-card").addClass("active");
    $(".archive-wrapper .archive-card").addClass("active");
  }

  const corruptionSwiper = new Swiper("#corruption-swiper", {
    slidesPerView: 1,
    spaceBetween: 10,
    loop: true,
    breakpoints: {
      1600: {
        slidesPerView: 4,
      },
      1280: {
        slidesPerView: 3.5,
        spaceBetween: 20,
      },
      992: {
        slidesPerView: 3,
        spaceBetween: 20,
      },
      576: {
        slidesPerView: 2,
        spaceBetween: 10,
      },
    },
    pagination: {
      el: ".part-corruption .swiper-pagination-number",
      type: "custom",
      renderCustom: function (swiper, current, total) {
        return current + "/" + total;
      },
    },
    navigation: {
      nextEl: ".part-corruption .swiper-next",
      prevEl: ".part-corruption .swiper-prev",
    },
  });
  if (window.innerWidth < 1280) {
    const cardSwiper = new Swiper("#card-swiper", {
      slidesPerView: 1,
      spaceBetween: 30,
      loop: true,
      autoplay: {
        delay: 3000,
        disableOnInteraction: false,
      },
      breakpoints: {
        768: {
          slidesPerView: 3,
          spaceBetween: 30,
        },
      },
      pagination: {
        el: "#card-swiper .swiper-pagination",
        clickable: true,
      },
    });
  }

  const storiesSwiper = new Swiper("#stories-swiper", {
    slidesPerView: 1,
    centeredSlides: true,
    spaceBetween: 30,
    loop: true,
    pagination: {
      el: "#stories-swiper .swiper-pagination",
      clickable: true,
    },
    breakpoints: {
      1280: {
        slidesPerView: 1.9,
        spaceBetween: 30,
      },
    },
  });

  $(".part-audio .audio-demo").each(function (index) {
    // 获取当前音频播放器的元素
    const $container = $(this);
    const $audio = $container.find("audio");
    const $playButton = $container.find(".play-button");
    const $progressFill = $container.find(".progress-fill");
    const $timeDisplay = $container.find(".time-display");
    const $playIcon = $container.find(".play-icon");
    const $pauseIcon = $container.find(".pause-icon");

    // 初始状态设置
    let isPlaying = false;
    let duration = 0;
    $pauseIcon.hide();

    // 格式化时间显示
    function formatTime(seconds) {
      const mins = Math.floor(seconds / 60);
      const secs = Math.floor(seconds % 60);
      return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
    }

    // 播放/暂停切换
    $playButton.on("click", function () {
      if (isPlaying) {
        $audio[0].pause();
        $playIcon.show();
        $pauseIcon.hide();
        isPlaying = false;
      } else {
        // 暂停所有其他正在播放的音频
        $(".audio-demo audio").each(function () {
          if (this !== $audio[0]) {
            this.pause();
            // 重置其他播放器的UI
            const $otherContainer = $(this).closest(".audio-demo");
            $otherContainer.find(".play-icon").show();
            $otherContainer.find(".pause-icon").hide();
          }
        });

        $audio[0].play();
        $playIcon.hide();
        $pauseIcon.show();
        isPlaying = true;
      }
    });

    // 音频元数据加载完成
    $audio.on("loadedmetadata", function () {
      duration = $audio[0].duration;
      $timeDisplay.text(formatTime(duration));
    });

    // 更新进度条和时间
    $audio.on("timeupdate", function () {
      if (duration > 0) {
        const progress = ($audio[0].currentTime / duration) * 100;
        $progressFill.css("width", progress + "%");

        // 显示剩余时间
        const remainingTime = duration - $audio[0].currentTime;
        $timeDisplay.text(formatTime(remainingTime));
      }
    });

    // 音频播放结束
    $audio.on("ended", function () {
      $playIcon.show();
      $pauseIcon.hide();
      isPlaying = false;
      $progressFill.css("width", "0%");
      $timeDisplay.text(formatTime(duration));
    });

    // 音频加载错误处理
    $audio.on("error", function () {
      console.error("音频加载失败");
      $timeDisplay.text("00:00");
    });
  });

  // 统一的自动切换间隔（毫秒）
  const AUDIO_SWIPER_INTERVAL = 4000;

  // 进度条动画函数
  function animateAudioProgressBar(activeIndex) {
    const $bars = $("#audio-accordion .audio-progress-bar");
    $bars.stop(true, true).css("width", "0%"); // 先全部重置
    $bars.each(function (i) {
      if (i === activeIndex) {
        $(this).animate({ width: "100%" }, AUDIO_SWIPER_INTERVAL, "linear");
      }
    });
  }

  // 手风琴同步展开
  function syncAccordion(activeIndex) {
    $("#audio-accordion .audio-accordion-header").each(function (i) {
      const target = $(this).data("target");
      if (i === activeIndex) {
        $(target).collapse("show");
      }
    });
  }

  // Swiper初始化
  const audioSwiper = new Swiper("#audio-swiper", {
    effect: "fade",
    fadeEffect: {
      crossFade: true,
    },
    allowTouchMove: false,
    noSwiping: true,
    simulateTouch: false,
    keyboard: {
      enabled: false,
    },
    autoplay: {
      delay: AUDIO_SWIPER_INTERVAL,
      disableOnInteraction: false,
    },
    on: {
      slideChange: function () {
        animateAudioProgressBar(this.realIndex);
        syncAccordion(this.realIndex);
      },
    },
  });

  // 手风琴点击时，切换swiper并同步进度条动画
  $("#audio-accordion .audio-accordion-header").each(function (index) {
    $(this).on("click", function (e) {
      const isExpanded = $(this).attr("aria-expanded") === "true";
      // 每次点击都重置 Swiper 自动切换计时

      // 每次点击都重置进度条动画
      animateAudioProgressBar(index);

      if (isExpanded) {
        // 阻止 Bootstrap collapse 的默认收起行为
        if (audioSwiper.autoplay) {
          audioSwiper.autoplay.stop();
          audioSwiper.autoplay.start();
        }
        e.stopPropagation();
        e.preventDefault();
        return false;
      }
      // 只有点击未展开项时才切换 Swiper
      audioSwiper.slideTo(index);
    });
  });

  // 页面初始化时，设置进度条动画和手风琴状态
  animateAudioProgressBar(audioSwiper.realIndex || 0);
  syncAccordion(audioSwiper.realIndex || 0);
});
