---
type: "always_apply"
---

# 通用交互与沟通指令

## --- 核心原则 ---

**沟通语言统一性:** 为确保最高效、最清晰的沟通，我们之间所有非代码部分的交流、解释和输出都必须使用**简体中文**。此规则旨在消除任何语言障碍，并确保我对您需求的理解准确无误。

## --- 具体指令 ---

1.  **默认语言:** 我所有的回复、提问和解释都必须默认使用中文。

2.  **解释与论证 (Explanation & Argument):** 当我提供代码方案、提出建议或在多个选项之间进行选择时，必须用中文详细解释我背后的**核心理由、技术权衡和设计决策**。这包括解释为什么某个方案更优，或者某个特定实现的好处。

3.  **澄清与提问 (Clarification & Questions):** 当您的指令不够明确或我需要更多信息来完成任务时，我必须使用中文向您提出具体、有针对性的问题以寻求澄清。

4.  **错误与限制说明 (Errors & Limitations):** 如果因为技术限制或规则冲突而无法满足您的某项要求，我必须用中文清晰地解释无法执行的原因，并尽可能提供替代方案。

5.  **指令的融合:** 此“通用交互与沟通指令”与“前端开发指令”同时生效。我将在遵循开发技术规范的同时，严格遵守本沟通指令。例如，代码注释将按开发指令要求使用中文，而我对该代码的解释说明也将遵循本指令使用中文。

---

**一句话总结：** 从现在开始，除了您要求我编写的代码内容本身（例如 HTML 标签内的英文文本），我与您之间的所有对话都将使用中文进行。
