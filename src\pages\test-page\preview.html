<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <meta name="description" content="" />
    <title>预览页面 - 开发预览</title>

    <!-- 可爱的预览页面Favicon 👀 -->
    <link rel="icon" type="image/svg+xml" href="/assets/favicons/eye-emoji.svg" />
    <link rel="shortcut icon" type="image/x-icon" href="/assets/favicons/eye-emoji.svg" />
    <link rel="apple-touch-icon" href="/assets/favicons/eye-emoji.svg" />

    <!-- 基础样式 -->
    <link rel="stylesheet" href="/shared/scss/_variables.scss" />
    <link rel="stylesheet" href="/shared/scss/_mixins.scss" />
    <link rel="stylesheet" href="/shared/scss/_base.scss" />
    <link rel="stylesheet" href="/shared/scss/_header.scss" />
    <link rel="stylesheet" href="/shared/scss/_footer.scss" />
    <link rel="stylesheet" href="./style.scss" />

    <!-- 页面专用样式 - 动态加载 -->
    <!-- <link rel="stylesheet" href="" id="page-style" /> -->

    <style>
      /* 加载状态样式 */
      .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(102, 126, 234, 0.9);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9998;
        opacity: 1;
        transition: opacity 0.3s ease;
      }

      .loading-overlay.fade-out {
        opacity: 0;
        pointer-events: none;
      }

      .loading-content {
        text-align: center;
        color: white;
      }

      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      @media (max-width: 768px) {
        .dev-preview-bar {
          flex-direction: column;
          gap: 10px;
          padding: 10px 15px;
        }

        .dev-preview-bar .info {
          flex-wrap: wrap;
          justify-content: center;
        }

        .dev-preview-bar .actions {
          flex-wrap: wrap;
          justify-content: center;
        }

        body {
          padding-top: 80px;
        }
      }
    </style>
  </head>
  <body data-pro="recoverit" data-cat="template" data-nav="basic" data-sys="auto" data-dev="auto">
    <!-- 加载状态覆盖层 -->
    <div class="loading-overlay" id="loading-overlay">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <h3>🚀 正在加载预览页面...</h3>
        <p>配置加载中，请稍候</p>
      </div>
    </div>

    <!-- 引入公共头部 -->
    <div id="header-placeholder"></div>

    <!-- 页面主要内容 -->
    <div id="main-placeholder"></div>

    <!-- 引入公共底部 -->
    <div id="footer-placeholder"></div>

    <!-- 公共脚本 -->
    <script type="module" src="/shared/js/common.js"></script>

    <!-- 预览生成器 -->
    <script type="module" src="/preview-generator.js"></script>

    <script>
      // 预览页面加载状态管理
      document.addEventListener("DOMContentLoaded", () => {
        const loadingOverlay = document.getElementById("loading-overlay");

        // 等待预览生成器初始化完成
        const waitForPreviewGenerator = () => {
          if (window.previewGenerator && typeof window.previewGenerator.init === "function") {
            // 监听预览生成器初始化完成
            const originalInit = window.previewGenerator.init;
            window.previewGenerator.init = async function () {
              try {
                // 调用原始初始化方法（它会自动获取页面ID并加载脚本）
                await originalInit.call(this);

                // 初始化完成，隐藏加载覆盖层
                setTimeout(() => {
                  loadingOverlay.classList.add("fade-out");
                  setTimeout(() => {
                    loadingOverlay.style.display = "none";
                  }, 300);
                }, 500);
              } catch (error) {
                // 初始化失败，也要隐藏加载覆盖层
                loadingOverlay.classList.add("fade-out");
                setTimeout(() => {
                  loadingOverlay.style.display = "none";
                }, 300);
                throw error;
              }
            };
          } else {
            // 如果预览生成器还没有加载，等待一段时间再检查
            setTimeout(waitForPreviewGenerator, 100);
          }
        };

        waitForPreviewGenerator();
      });
    </script>
  </body>
</html>
