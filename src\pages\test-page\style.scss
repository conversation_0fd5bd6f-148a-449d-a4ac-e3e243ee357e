* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

main {
  color: #000;
  background-color: #f6faff;
  font-family: "Mulish", -apple-system, blinkmacsystemfont, "Segoe UI", roboto, "Helvetica Neue", arial, "Noto Sans", sans-serif, "Apple Color Emoji",
    "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";

  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  p,
  div,
  span {
    margin-bottom: 0;
    font-family: "Mulish", -apple-system, blinkmacsystemfont, "Segoe UI", roboto, "Helvetica Neue", arial, "Noto Sans", sans-serif, "Apple Color Emoji",
      "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  }

  h1,
  h2,
  h3 {
    text-align: center;
  }
  h2 {
    font-size: 2.25rem;
    font-weight: 800;
  }

  .opacity-6 {
    opacity: 0.6;
  }

  .opacity-7 {
    opacity: 0.7;
  }

  .blue-text {
    color: #0085ff;
  }

  .blue-link {
    color: #0085ff;
    &:hover {
      color: #0085ff;
    }
  }

  .btn-wrapper {
    display: flex;

    justify-content: center;
    gap: 1rem;
    @media (max-width: 768px) {
      flex-direction: column;
      gap: 8px;
    }
    .btn {
      margin: 0;
      border-radius: 4px;
      text-transform: capitalize;
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 160px;
    }
  }

  .btn-outline-secondary {
    color: #349eff;
    border-color: #349eff;
  }

  .btn-outline-secondary:hover {
    color: #fff;
    background-color: #349eff;
    border-color: #349eff;
  }

  .btn {
    gap: 0.5rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }
  .btn-download {
    background: linear-gradient(259.15deg, #59b0ff 42.76%, #0085ff 100%), linear-gradient(0deg, #006dff, #006dff);
    border: none;
    color: #fff;
    transition: unset;
    text-transform: capitalize;

    &:hover {
      color: #fff;
      background: #0085ff;
    }
  }

  .swiper-pagination-bullet {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #e3eefc;
    opacity: 1;
    bottom: -4px;
    &.swiper-pagination-bullet-active {
      width: 4rem;
      border-radius: 999px;
      background: linear-gradient(259.15deg, #59b0ff 42.76%, #0085ff 100%);
    }
  }

  .btn-white {
    color: #0c7dfa;
    text-transform: capitalize;
    &:hover,
    &:focus,
    &:active {
      background: #006dff;
      color: #fff;
      border-color: #006dff;
    }
  }
  .part-banner {
    position: relative;
    .swiper-content {
      position: absolute;
      top: 45%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      z-index: 2;
      margin: 0 auto;
      color: #fff;
      .content-left {
        .banner-title {
          font-size: 4rem;
          font-weight: 700;
          text-shadow: 0px 4px 4px #00000040;
          text-align: left;
          margin-bottom: 1.125rem;
        }
        .banner-desc {
          font-size: 1.25rem;
          font-weight: 700;
          color: #fff;
          text-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.25);
        }
      }
      .content-right {
        flex: 0 1 25%;

        @media (max-width: 1600px) {
          flex: 0 1 40%;
        }

        .nav-wrapper {
          border-radius: 1.5rem;
          backdrop-filter: blur(14px);
          padding: 2.375rem 1.875rem;
          background: rgba(0, 0, 0, 0.3);
        }

        .nav {
          display: flex;
          flex-direction: column;
        }

        .nav .nav-item {
          text-decoration: none;
          color: #fff;
          overflow: hidden;
          border-bottom: 1px solid rgba(255, 255, 255, 0.3);
        }

        .nav .nav-item.active .progress-bar-fill {
          width: 100% !important;
          transition: all linear 3.5s !important;
        }

        .nav .nav-item .collapse-item {
          font-weight: 500;
          font-size: 1.125rem;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 10px 0;
          color: rgba(255, 255, 255, 0.8);
          transition: none;
        }

        .nav .nav-item .nav-item-title {
          font-size: 1.125rem;
          color: inherit;
        }

        @keyframes progressBar {
          0% {
            transform: translateX(-101%);
          }

          100% {
            transform: translateX(0);
          }
        }

        .nav .nav-item .collapse-detail .quote-text {
          color: rgba(255, 255, 255, 0.7);
          font-size: 0.875rem;
          font-style: italic;
        }

        .nav .nav-item .collapse-detail .quote-author {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          gap: 10px;
          padding: 1px 0 0.625rem;
        }

        .nav .nav-item .collapse-detail .quote-author .quote-author-img {
          width: 2rem;
          height: 2rem;
        }

        .nav .nav-item .collapse-detail .quote-author .quote-author-name {
          font-size: 14px;
          color: #93ffcd;
        }

        .nav .nav-item .collapse-detail .progress-bar {
          width: 100%;
          height: 2px;
          overflow: hidden;
          background-color: transparent;
          position: relative;
        }

        .nav .nav-item .collapse-detail .progress-bar .progress-bar-fill {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          animation: progressBar 3.5s linear;
          height: 100%;
          background: #5dffb4;
        }

        .nav .nav-item.active .collapse-item {
          color: #5dffb4;
          font-weight: 700;
          font-size: 1.25rem;
        }

        .nav .nav-item:has([aria-expanded="true"]) svg {
          transform: rotate(180deg);
        }
      }
    }
    // 小屏幕移动端banner
    .situation-mobile-box {
      position: relative;
      .situation-mobile-img {
        max-height: 800px;
        object-fit: cover;
        object-position: center 12%;
      }
      .situation-mobile-content {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        width: 100%;
        padding: 0 24px;
        .mobile-title {
          text-align: center;
          font-size: 28px;
          font-weight: 700;
          color: #fff;
          text-shadow: 0px 2.18px 2.18px 0px #00000040;
          margin-bottom: 8px;
        }
        .mobile-desc {
          text-align: center;
          font-size: 12px;
          color: #fff;
          margin-bottom: 16px;
          text-shadow: 0px 1.09px 2.18px 0px #00000040;
        }
        .situation-mobile-quote {
          padding: 16px 4px 24px;
          display: flex;
          flex-direction: column;
          align-items: center;
          @media (max-width: 768px) {
            align-items: flex-start;
          }

          .situation-mobile-title {
            font-weight: 16px;
            font-weight: 700;
            color: #5dffb4;
            margin-bottom: 8px;
          }
          .situation-mobile-text {
            font-size: 12px;
            color: #fff;
            margin-bottom: 8px;
            font-style: italic;
          }
          .situation-mobile-author {
            display: flex;
            align-items: center;
            gap: 10px;
            .situation-mobile-author-img {
              width: 32px;
              height: 32px;
            }
            .situation-mobile-author-name {
              font-size: 14px;
              color: #93ffcd;
            }
          }
        }
      }
    }
  }
  .part-files {
    background-color: #f6faff;
    .file-box {
      height: 100%;
      display: flex;
      flex-direction: column;
      border-radius: 1rem;
      overflow: hidden;
    }

    .file-box .file-box-content {
      background-color: #fff;
      padding: 2rem 1.5rem;
      flex: 1;
      display: flex;
      flex-direction: column;

      p {
        color: #787878;
      }
    }

    @media (max-width: 576px) {
      .file-box .file-box-content {
        padding: 8px;
      }
    }

    .file-box .file-box-content .box-title {
      font-weight: 700;
      font-size: 1.25rem;
      color: #000;
      text-decoration: none;
      display: inline-block;
      margin-bottom: 0.75rem;
      text-align: left;
      line-height: 100%;
    }

    @media (max-width: 576px) {
      .col-6 {
        padding-right: 8px;
        padding-left: 8px;
      }

      .col-6:nth-child(odd) {
        padding-right: 4px;
      }

      .col-6:nth-child(even) {
        padding-left: 4px;
      }
    }
  }
  .part-methods {
    background-color: #e4efff;
    .methods-navigation {
      display: flex;
      gap: 0.75rem;
      justify-content: center;
      @media (max-width: 768px) {
        flex-wrap: wrap;
      }
      .methods-navigation-item {
        flex: 1;
        padding: 0.75rem;
        border-radius: 0.5rem;
        border: 1px solid #1a8dff;
        background-color: #fff;
        text-align: center;
        font-size: 1.25rem;
        color: #1a8dff;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        @media (max-width: 768px) {
          flex: 1 1 calc(50% - 0.375rem);
          font-weight: 400 !important;
        }
        &.active {
          background-color: #1a8dff;
          color: #fff;
          font-weight: 700;
        }
      }
    }
    .method-box {
      display: flex;
      border-radius: 0.75rem;
      background-color: #fff;
      padding: 1.75rem;
      justify-content: space-between;
      gap: 2.125rem;

      @media (max-width: 1600px) {
        align-items: center;
      }
      @media (max-width: 1280px) {
        flex-direction: column;
        gap: 1rem;
      }

      .method-img {
        flex: 0 0 44.5%;
      }
      .method-content {
        padding-right: 1rem;
        flex: 1;
        @media (max-width: 1280px) {
          padding-right: unset;
          width: 100%;
        }
        .method-compare {
          margin-top: 1.5rem;
          border-radius: 1rem;
          background-color: #e4efff;
          padding: 1.5rem 2rem;
          display: flex;
          justify-content: space-between;
          gap: 1.875rem;
          @media (max-width: 768px) {
            flex-direction: column;
            gap: 1rem;
            padding: 1rem;
          }
          .method-adv {
            flex: 1;
            .method-adv-title {
              font-size: 1.25rem;
              font-weight: 700;
              color: #0074e8;
              margin-bottom: 0.75rem;
            }
            .method-adv-list {
              display: flex;
              flex-direction: column;
              gap: 0.75rem;
              .method-adv-item {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                p {
                  font-size: 12px;
                }
              }
            }
          }
          .method-disadv {
            flex: 1;
            .method-disadv-title {
              font-size: 1.25rem;
              font-weight: 700;
              color: #ff3636;
              margin-bottom: 0.75rem;
            }
            .method-disadv-list {
              display: flex;
              flex-direction: column;
              gap: 0.75rem;
              .method-disadv-item {
                display: flex;
                align-items: flex-start;
                gap: 0.5rem;
                p {
                  font-size: 12px;
                }
              }
            }
          }
        }
      }
    }
    .product-download {
      margin-top: 1.5rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      @media (max-width: 768px) {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
      }
      .product-download-info {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        .product-download-text {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
          .product-download-title {
            font-size: 1.25rem;
            font-weight: 700;
            line-height: 100%;
          }
          .product-download-desc {
            font-size: 0.875rem;
          }
        }
      }
    }
    .left-btn,
    .right-btn {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      cursor: pointer;
      width: 3rem;
      height: 3rem;
      border-radius: 50%;
      background-color: #f8f8f8;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #bed8ff;
      &:hover {
        background-color: #006dff;
        color: #fff;
      }
    }
    .left-btn {
      left: -5rem;
    }
    .right-btn {
      right: -5rem;
    }
  }
  .part-Keys {
    background: linear-gradient(to bottom, #e4efff 0%, #e4efff 67%, #f6faff 67%, #f6faff 100%);

    @media (min-width: 992px) {
      #swiper-keys .swiper-wrapper {
        gap: 1.875rem;
        flex-wrap: wrap;
      }

      #swiper-keys .swiper-wrapper .swiper-slide {
        flex: 1 1 calc(33% - 1.875rem);
      }
    }
    .key-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;
      padding: 2rem;
      gap: 0.9375rem;
      border-radius: 0.75rem;
      overflow: hidden;
      box-shadow: 0px 40px 14px -28px #54c1ff21;
      position: relative;
      min-height: 248px;
      background-color: #fff;
      height: 100%;
      @media (max-width: 768px) {
        min-height: unset;
      }

      .tip-icon {
        width: 7.5rem;
        height: 7.5rem;
      }
      .text-detail {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        width: 100%;
        height: 100%;
        padding: 2rem;
        border-radius: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        background: linear-gradient(170.79deg, #59b0ff -1.84%, #0085ff 100.27%), linear-gradient(30.8deg, #34425b 47.27%, #768598 105.38%);
        transition: all 0.3s ease;
        transform: translateY(110%);
        color: #fff;

        p {
          font-size: 12px;
        }
      }
      &:hover {
        .text-detail {
          transform: translateY(0);
        }
        box-shadow: 0px 25px 15.5px -15px #66bbf9a8;
      }
    }
  }

  .part-storage {
    background-color: #f6faff;
    .system-item {
      height: 100%;
      border-radius: 1rem;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      .system-item-content {
        padding: 2.25rem 2rem;
        background-color: #e6f2ff;
        flex: 1;

        .system-item-title {
          font-weight: 800;
          font-size: 1.5rem;
          margin-bottom: 1rem;
        }
        .system-item-desc {
          font-size: 0.875rem;
          opacity: 0.7;
        }
      }
    }
  }
  .part-user {
    /* 
  通用卡片滑块/悬停组件样式
*/
    .card-slider-container {
      // --- 可配置变量 ---
      --card-count: 5; // 【重要】桌面端卡片总数
      --gap: 0.5rem; // 卡片间距
      --active-width: 38.72%; // 展开卡片的宽度
      // -----------------

      .swiper-wrapper {
        // 移动端默认为 Swiper 的 flex 布局
        // 桌面端自定义布局
        @media (min-width: 1280px) {
          display: flex;
          gap: var(--gap);
          aspect-ratio: 1410 / 660; // 容器宽高比，可根据需要调整
          justify-content: center;
        }
      }

      .swiper-slide {
        transition: width 0.4s ease-in-out;
        height: 100%;
        overflow: hidden;

        @media (min-width: 1280px) {
          // 使用 calc() 动态计算未展开卡片的宽度
          // (总宽度 100% - 激活卡片宽度 - 所有间距) / (卡片总数 - 1)
          --inactive-width: calc((100% - var(--active-width) - (var(--card-count) - 1) * var(--gap)) / (var(--card-count) - 1));
          width: var(--inactive-width);

          &.is-active {
            width: var(--active-width);

            .user-card-content {
              padding: 2rem;
            }
            .user-card-title {
              opacity: 1;
            }
            .user-card-desc {
              display: block;
              opacity: 0.7;
            }
          }
        }
      }

      // Swiper 分页器样式
      .swiper-pagination-bullet {
        background-color: #e3eefc;
        opacity: 0.5;
        &.swiper-pagination-bullet-active {
          background-color: #007aff;
          opacity: 1;
        }
      }
    }

    .user-card-item {
      width: 100%;
      height: 100%;
      position: relative;
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      border-radius: 1rem;
      overflow: hidden;
      color: #fff;
      background-image: var(--bg-image);
      background-position-x: var(--bg-position-x);

      // 移动端卡片宽高比
      @media (max-width: 1279.98px) {
        aspect-ratio: 530 / 640;
      }

      // 底部渐变遮罩，增强文字可读性
      &::after {
        content: "";
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
        background-size: 100% 50%;
        background-position: center bottom;
        background-repeat: no-repeat;
      }
    }

    .user-card-content {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      z-index: 2;
      padding: 2rem 0.75rem;
      box-sizing: border-box;

      .user-card-title {
        font-weight: 800;
        font-size: 1.5rem;
        margin-top: 0;
        text-align: left;
        opacity: 0.3;
        @media (max-width: 1600px) {
          font-size: 1.25rem;
        }
        @media (max-width: 1280px) {
          opacity: 1;
        }
      }

      .user-card-desc {
        font-size: 0.875rem;
        margin: 0;
        margin-top: 1rem;
        // 桌面端默认隐藏
        @media (min-width: 1280px) {
          display: none;
        }
        // 移动端默认显示
        @media (max-width: 1279.98px) {
          display: block;
          opacity: 0.7;
        }
      }
    }
  }
  .part-highlights {
    background-color: #f5f8ff;
    .btn-white {
      color: #0c7dfa;
      border-radius: 0.75rem;
      &:hover {
        color: #fff;
      }
    }
    .blue-cricle1 {
      position: absolute;
      left: 41%;
      bottom: 42%;
      width: 23%;
      animation: icon-rotate 3s linear infinite;
    }
    .blue-cricle2 {
      position: absolute;
      left: 40%;
      bottom: 42%;
      width: 21%;
      animation: icon-rotate 3s linear infinite;
    }
    .win-icon-link {
      position: absolute;
      left: 56%;
      bottom: 55%;
      width: 27%;
    }
    .mac-icon-link {
      position: absolute;
      left: 22%;
      bottom: 55%;
      width: 27%;
    }

    .assetsSwiper-box {
      position: relative;
    }

    @media (min-width: 1280px) {
      .assetsSwiper-box .assetsSwiper .swiper-wrapper {
        gap: 8px;
        justify-content: space-between;
        @media (max-width: 1600px) {
          gap: 8px;
        }
      }

      .assetsSwiper-box .assetsSwiper .swiper-slide {
        width: 7.5%;
        display: block;
        height: auto;
        overflow: hidden;
        border-radius: 1rem;
        position: relative;
        transition: 0.8s cubic-bezier(0.05, 0.61, 0.41, 0.95);
        min-height: 430px;
        @media (max-width: 1600px) {
          min-height: 400px;
        }
      }

      .assetsSwiper-box .assetsSwiper .swiper-slide .box-style {
        padding: 1.875rem 1.5rem;
        width: 100%;
        height: 100%;
        position: relative;
        border-radius: 1.5rem;
        color: #fff;
        background: linear-gradient(269.24deg, #6ba7ff -43.65%, #2d84ff 54.28%);

        overflow: hidden;
      }

      .assetsSwiper-box .assetsSwiper .swiper-slide .box-style .right-icon {
        position: absolute;
        right: 2.1rem;
        top: 1.875rem;
      }

      .assetsSwiper-box .assetsSwiper .swiper-slide .box-style .box-title {
        color: #fff;
        bottom: 1.125rem;
        left: 2.125rem;
        position: absolute;
        font-size: 1.25rem;
        writing-mode: sideways-lr;
        height: 525px;
        z-index: 3;
        width: 0%;
        transition: 0.8s cubic-bezier(0.05, 0.61, 0.41, 0.95);
      }

      .assetsSwiper-box .assetsSwiper .swiper-slide .box-style .card-content {
        opacity: 0;
        height: calc(100% - 3.875rem);
        width: 100%;
        display: flex;
        margin-top: 3.875rem;
        padding-top: 2.25rem;
        border-top: 1px solid rgba($color: #f2f2f2, $alpha: 0.3);
        justify-content: space-between;
        // gap: 3rem;
        min-width: 774px;
        transition: 0.8s cubic-bezier(0.05, 0.61, 0.41, 0.95);
      }
    }

    @media (min-width: 1280px) and (max-width: 1600px) {
      .assetsSwiper-box .assetsSwiper .swiper-slide .box-style .card-content {
        min-width: 634px;
      }
    }

    @media (min-width: 1280px) {
      .assetsSwiper-box .assetsSwiper .swiper-slide .box-style .card-content .left-img-wrapper {
        flex: 0 0 45%;
        margin-top: -2.25rem;
        margin-left: -1.5rem;
        margin-bottom: -1.875rem;
        @media (max-width: 1600px) {
          flex: 0 0 51%;
        }
      }

      .assetsSwiper-box .assetsSwiper .swiper-slide .box-style .card-content .right-detial-wrapper {
        flex: 1;
        padding-top: 1.5rem;
        display: flex;
        justify-content: space-between;
        flex-direction: column;
        color: #fff;
        padding-bottom: 3rem;
        @media (max-width: 1600px) {
          padding-top: unset;
          padding-bottom: unset;
        }
      }

      .assetsSwiper-box .assetsSwiper .swiper-slide.active {
        width: 58.3%;
        opacity: 1;
      }

      .assetsSwiper-box .assetsSwiper .swiper-slide.active .box-style .box-title {
        transform: rotate(90deg);
        transform-origin: bottom left;
        bottom: 95%;
        font-size: 2rem;
        font-weight: 600;
      }

      .assetsSwiper-box .assetsSwiper .swiper-slide.active .box-style .card-content {
        opacity: 1;
      }

      @keyframes fadeIn {
        from {
          visibility: hidden;
        }

        to {
          visibility: visible;
        }
      }
    }

    @media (max-width: 1280px) {
      .assetsSwiper-box .box-style {
        height: 100%;
        background: #2c84ff;

        border-radius: 1.5rem;
        overflow: hidden;
        padding: 1.5rem;
      }

      .assetsSwiper-box .box-style .top-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 1.5rem;
        border-bottom: 1px solid #f2f2f2;
      }

      .assetsSwiper-box .box-style .top-content .right-icon {
        width: 2.5rem;
      }

      .assetsSwiper-box .box-style .top-content .box-title {
        font-size: 2rem;
        font-weight: 600;
        color: #fff;
      }
    }

    @media (max-width: 1280px) and (max-width: 768px) {
      .assetsSwiper-box .box-style .top-content .box-title {
        font-size: 1.5rem;
      }
    }

    @media (max-width: 1280px) {
      .assetsSwiper-box .box-style .card-content {
        width: 100%;
        display: flex;
        justify-content: space-between;
        gap: 1rem;
      }
    }

    @media (max-width: 1280px) and (max-width: 768px) {
      .assetsSwiper-box .box-style .card-content {
        flex-direction: column;
        align-items: center;
      }
    }

    @media (max-width: 1280px) {
      .assetsSwiper-box .box-style .card-content .left-img-wrapper {
        flex: 0 0 35%;
        max-width: 240px;
      }
    }

    @media (max-width: 1280px) and (max-width: 768px) {
      .assetsSwiper-box .box-style .card-content .left-img-wrapper {
        order: 2;
      }
    }

    @media (max-width: 1280px) {
      .assetsSwiper-box .box-style .card-content .right-detial-wrapper {
        flex: 1;
        padding-top: 1.5rem;
        display: flex;
        justify-content: space-between;

        flex-direction: column;
      }
    }

    @media (max-width: 1280px) and (max-width: 768px) {
      .assetsSwiper-box .box-style .card-content .right-detial-wrapper {
        order: 1;
      }
    }

    @media (max-width: 1280px) {
      .assetsSwiper-box .assetsSwiper .swiper-slide {
        height: auto;
      }

      .assetsSwiper-box .assetsSwiper .rounded-16 {
        border-radius: 8px;
      }
    }
  }
  .part-table {
    .table-box {
      @media (max-width: 1280px) {
        overflow: auto;
        overflow-y: hidden;
      }
    }
    .table-wrapper {
      /* --- 1. 定制化变量 --- */
      --table-theme-color: linear-gradient(24.6deg, #0085ff 12.84%, #59b0ff 88.13%); /* 主要主题色，用于高亮列 */
      --table-theme-color-border: #0085ff; /* 边框和外框颜色 */
      --table-side-col-bg: #ebf4ff; /* 侧边和头部非高亮背景色 */
      --table-main-bg: #f2fbff; /* 表格主体背景色 */
      --table-text-primary: #000; /* 主要文字颜色 */
      --table-text-secondary: #fff; /* 次要文字颜色 (高亮列上) */
      --table-border-radius: 1rem; /* 表格圆角大小 */
      --table-decorator-height: 1.5rem; /* 高亮列顶部和底部的装饰条高度 */

      /* --- 2. 基础布局和容器 --- */
      border-radius: var(--table-border-radius);
      width: 100%;
      position: relative;
      overflow: visible;
      background-color: var(--table-theme-color-border); /* 用背景色模拟外边框 */
      padding: 1.2px; /* 关键：让背景色显示为1px的边框 */
      margin-top: 6.25rem;
      margin-bottom: 6.25rem;
      min-width: 928px;
      @media (max-width: 576px) {
        min-width: 728px;
      }
    }

    .inner-table {
      border-collapse: collapse; /* 合并边框 */
      border-style: hidden; /* 隐藏表格默认边框，由wrapper接管 */
      width: 100%;
      background: var(--table-main-bg);
      border-radius: var(--table-border-radius);
      overflow: auto; /* 必须设置，以显示高亮列的装饰条 */
      table-layout: fixed;
    }

    /* --- 3. 单元格通用样式 --- */
    .inner-table th,
    .inner-table td {
      position: relative; /* 为伪元素定位提供基准 */
      padding: 1rem;
      text-align: center;
      vertical-align: middle;
      width: calc(100% / 5); /* 平均分配列宽，5是总列数 */
      @media (max-width: 1600px) {
        padding: 1rem 8px;
      }
    }

    .inner-table th {
      font-weight: 700;
      font-size: 1rem;
      color: var(--table-text-primary);
      background-color: var(--table-side-col-bg);
    }

    .inner-table td {
      font-size: 1rem;
      @media (max-width: 1280px) {
        font-size: 12px;
      }
    }

    /* 行标题列 (第一列) 的特殊样式 */
    .inner-table td:first-child {
      background-color: var(--table-side-col-bg);
      font-size: 1rem;
      font-weight: 700;
      color: var(--table-text-primary);
      padding: 16px;
      @media (max-width: 1280px) {
        font-size: 12px;
        padding: 1rem 8px;
      }
    }

    /* --- 4. 高亮列样式 --- */
    /* 整列渐变效果：使用伪元素在表格容器上创建渐变背景 */
    .table-wrapper::before {
      content: "";
      position: absolute;
      top: calc(1.2px - 1.5rem); /* 向上多出24px */
      left: calc(20% - 1.2px); /* 第二列的位置 */
      width: calc(20% + 2.4px); /* 列宽减去左右边框 */
      height: calc(100% + 5rem + 1.5rem); /* 增加高度以覆盖下载按钮区域和上方24px */
      background: var(--table-theme-color); /* 使用您指定的斜向渐变 */
      border-radius: 0 0 0.5rem 0; /* 添加底部圆角以匹配下载区域 */
      z-index: 1;
      pointer-events: none; /* 确保不影响表格交互 */
      border-radius: 1rem;
    }

    /* 高亮列单元格样式：移除背景，只设置文字颜色和层级 */
    .inner-table .highlight-col {
      background: transparent !important; /* 强制移除单独的背景 */
      color: var(--table-text-secondary);
      position: relative;
      z-index: 2; /* 确保内容在渐变背景之上 */
      @media (max-width: 1280px) {
        min-width: 10.75rem;
      }
    }
    .highlight-col-top {
      position: relative;
      font-size: 1.25rem !important;
    }

    /* 高亮列顶部的装饰条 */
    .highlight-col-top::before {
      content: "";
      position: absolute;
      top: 0;
      left: -1px;
      width: calc(100% + 2px);
      height: var(--table-decorator-height);
      background-color: var(--table-theme-color);
      border-radius: 0.5rem 0.5rem 0 0;
      transform: translateY(-98%);
    }

    .highlight-col-top .RP-logo {
      width: 25.7%;
      bottom: 92%;
      left: 50%;
      position: absolute;
      transform: translate(-50%, 0);
      // transform: translate(0, 100%);
    }

    /* 高亮列底部的装饰条 */
    .highlight-col-bottom {
      .download-item {
        position: absolute;
        bottom: 0;
        left: -1px;
        width: calc(100% + 2px);
        padding: 0.75rem 0;
        background: transparent; /* 改为透明，让渐变背景显示出来 */
        border-radius: 0 0 0.5rem 0.5rem;
        transform: translateY(98%);
        z-index: 2; /* 确保按钮内容在渐变背景之上 */
        opacity: 1;
      }
    }

    /* --- 5. 边框样式 --- */
    /* 垂直边框 */
    .inner-table th:not(:last-child),
    .inner-table td:not(:last-child) {
      border-right: 1px solid var(--table-theme-color-border);
    }

    /* 水平边框 (非高亮列) */
    .inner-table th:not(.highlight-col)::after,
    .inner-table tr:not(:last-child) td:not(.highlight-col)::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 1.1px;
      background-color: var(--table-theme-color-border);
    }

    /* 水平边框 (高亮列)，颜色稍浅 */
    .inner-table th.highlight-col::after,
    .inner-table tr:not(:last-child) td.highlight-col::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 2rem;
      right: 2rem;
      height: 1.1px;
      background-color: #8ed9ff;
      @media (max-width: 768px) {
        left: 8px;
        right: 8px;
      }
    }

    /* --- 6. 内容对齐和响应式 --- */
    .inner-table td span {
      vertical-align: middle;
      margin-left: 8px;
      opacity: 0.7;
    }
    .inner-table td div {
      opacity: 0.7;
    }

    /* 移动端响应式调整 */
    @media (max-width: 768px) {
      .inner-table th {
        font-size: 10px;
        padding: 8px 4px;
      }
      .inner-table td {
        font-size: 9px;
        padding: 8px 4px;
      }
      .inner-table td:first-child {
        font-size: 10px;
        padding: 8px;
      }
      .inner-table td img {
        width: 14px;
      }
      .mt-logo {
        width: 60%;
      }
    }

    .blue-tip {
      background-color: #4dacff;
      color: #fff;
      border-radius: 4px;
      padding: 0.1875rem 0.25rem;
      color: #fff;
      font-size: 0.75rem;
      line-height: 100%;
      margin: auto 4px;
      @media (max-width: 576px) {
        font-size: 8px;
      }
    }

    .blue-tip2 {
      background-color: #a8c4dd;
      color: #fff;
      border-radius: 4px;
      padding: 0.1875rem 0.25rem;
      color: #fff;
      font-size: 0.75rem;
      line-height: 100%;
      margin: auto 4px;
      @media (max-width: 576px) {
        font-size: 8px;
      }
    }
  }
  .part-faq {
    .wsc-icon {
      position: relative;
      height: 1rem;
      line-height: 0.5;
      vertical-align: inherit;
    }

    .accordion i {
      transition: 0.2s;
    }

    .accordion [aria-expanded="true"] i {
      transform: rotate(180deg);
      color: #1891ff;
    }
  }
  .part-links {
    .part-links-line {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }

    .line-border {
      border-right: 1px solid rgba(0, 0, 0, 0.3);
      border-left: 1px solid rgba(0, 0, 0, 0.3);
    }

    @media (max-width: 1280px) {
      .line-border {
        border-right: unset;
      }
    }

    @media (max-width: 768px) {
      .line-border {
        border-left: unset;
      }
    }

    .text-link {
      font-size: 0.875rem;
      color: rgba(0, 0, 0, 0.7);
      margin-top: 1.5rem;
      display: block;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .text-link:hover {
      color: #0055fb;
    }

    .part-links-videos {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }

    .part-links-videos .video-wrapper {
      border-radius: 0.75rem;
    }

    @media (max-width: 1280px) {
      .part-links-videos {
        flex-direction: row;
        padding-top: 2rem;
      }
    }

    @media (max-width: 576px) {
      .part-links-videos {
        display: block;
      }
    }

    .text-line4 {
      display: -webkit-box;
      -webkit-line-clamp: 4;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  }
  .part-footer .footer-box {
    background: url(https://images.wondershare.com/recoverit/images2025/email/footer-bg.jpg) no-repeat center center/cover;
    border-radius: 1rem;
    overflow: hidden;
    padding: 0 2.25rem;
    color: #fff;
    @media (max-width: 768px) {
      padding: 2rem;
    }
  }

  .part-footer .footer-box .footer-title {
    font-weight: 700;
    font-size: 2.5rem;
    @media (max-width: 768px) {
      text-align: center;
      font-size: 2.25rem;
    }
  }

  .part-footer .btn-white {
    color: #349eff;
    &:hover,
    &:focus {
      background-color: #ececec;
      border-color: #e6e6e6;
    }
  }

  .part-footer .btn-outline-white:hover,
  .part-footer .btn-outline-white:focus {
    color: #349eff;
  }
}
