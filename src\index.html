<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>页面构建器 - 开发预览</title>

    <!-- 可爱的开发预览Favicon 💝 -->
    <link rel="icon" type="image/png" href="https://raw.githubusercontent.com/lukeocodes/dev-hearts/main/build/sparkling_heart.png" />
    <link rel="apple-touch-icon" href="https://raw.githubusercontent.com/lukeocodes/dev-hearts/main/build/sparkling_heart.png" />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
        min-height: 100vh;
        padding: 40px 20px;
        position: relative;
      }

      body::before {
        content: "";
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hearts" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><text x="10" y="15" text-anchor="middle" font-size="8" fill="%23ffffff" opacity="0.1">💕</text></pattern></defs><rect width="100" height="100" fill="url(%23hearts)"/></svg>');
        pointer-events: none;
        z-index: -1;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
      }

      .header {
        text-align: center;
        color: white;
        margin-bottom: 60px;
      }

      .header h1 {
        font-size: 3rem;
        margin-bottom: 20px;
        font-weight: 700;
        text-shadow: 0 2px 10px rgba(255, 107, 157, 0.3);
        color: white;
        animation: sparkle 2s ease-in-out infinite alternate;
      }

      @keyframes sparkle {
        0% {
          filter: brightness(1) saturate(1);
        }
        100% {
          filter: brightness(1.1) saturate(1.2);
        }
      }

      .header p {
        font-size: 1.2rem;
        opacity: 0.9;
        max-width: 600px;
        margin: 0 auto;
        line-height: 1.6;
      }

      .pages-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 30px;
        margin-bottom: 60px;
      }

      .page-card {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(255, 154, 158, 0.2);
        transition: all 0.4s ease;
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.5);
      }

      .page-card:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 25px 60px rgba(255, 154, 158, 0.3);
        background: rgba(255, 255, 255, 0.95);
      }

      .page-card::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #ff9a9e, #fad0c4, #a8edea);
      }

      .page-card h3 {
        font-size: 1.5rem;
        color: #333;
        margin-bottom: 15px;
        font-weight: 600;
      }

      .page-card p {
        color: #666;
        margin-bottom: 25px;
        line-height: 1.6;
      }

      .page-card .meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 25px;
        font-size: 0.9rem;
        color: #888;
      }

      .page-card .actions {
        display: flex;
        gap: 15px;
      }

      .btn {
        padding: 12px 24px;
        border-radius: 25px;
        border: none;
        cursor: pointer;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
      }

      .btn-primary {
        background: linear-gradient(135deg, #ff9a9e, #fad0c4);
        color: white;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }

      .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 154, 158, 0.4);
        background: linear-gradient(135deg, #ff8a95, #f9c5d1);
      }

      .btn-outline {
        background: rgba(255, 255, 255, 0.2);
        color: #ff6b9d;
        border: 2px solid #ff6b9d;
        backdrop-filter: blur(10px);
      }

      .btn-outline:hover {
        background: linear-gradient(135deg, #ff6b9d, #c44569);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 107, 157, 0.3);
      }

      .stats {
        background: rgba(255, 255, 255, 0.25);
        border-radius: 20px;
        padding: 30px;
        text-align: center;
        color: white;
        backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 8px 32px rgba(255, 154, 158, 0.2);
      }

      .stats h3 {
        font-size: 1.5rem;
        margin-bottom: 20px;
        opacity: 0.9;
      }

      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 30px;
      }

      .stat-item {
        text-align: center;
      }

      .stat-item .number {
        font-size: 2.5rem;
        font-weight: 700;
        display: block;
        margin-bottom: 8px;
      }

      .stat-item .label {
        font-size: 0.9rem;
        opacity: 0.8;
      }

      .empty-state {
        text-align: center;
        color: white;
        padding: 60px 20px;
      }

      .empty-state h3 {
        font-size: 1.5rem;
        margin-bottom: 15px;
        opacity: 0.9;
      }

      .empty-state p {
        margin-bottom: 30px;
        opacity: 0.8;
        line-height: 1.6;
      }

      .empty-state .btn {
        background: rgba(255, 255, 255, 0.25);
        color: white;
        border: 2px solid rgba(255, 255, 255, 0.4);
        backdrop-filter: blur(10px);
      }

      .empty-state .btn:hover {
        background: linear-gradient(135deg, rgba(255, 107, 157, 0.8), rgba(196, 69, 105, 0.8));
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 107, 157, 0.3);
      }

      @media (max-width: 768px) {
        .header h1 {
          font-size: 2rem;
        }

        .pages-grid {
          grid-template-columns: 1fr;
        }

        .page-card .actions {
          flex-direction: column;
        }

        .stats-grid {
          grid-template-columns: repeat(2, 1fr);
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>💝 页面构建器 ✨</h1>
        <p>💕 模块化单页面开发工具 - 专注于main区域，头部底部保持固定 💕</p>
      </div>

      <div id="pages-container">
        <!-- 页面列表将通过JavaScript动态加载 -->
      </div>

      <div class="stats">
        <h3>🌸 项目统计 ✨</h3>
        <div class="stats-grid">
          <div class="stat-item">
            <span class="number" id="page-count">0</span>
            <span class="label">💝 页面数量</span>
          </div>
          <div class="stat-item">
            <span class="number">3</span>
            <span class="label">🎀 核心部分</span>
          </div>
          <div class="stat-item">
            <span class="number">Vite</span>
            <span class="label">🌟 构建工具</span>
          </div>
          <div class="stat-item">
            <span class="number">SCSS</span>
            <span class="label">💖 样式预处理</span>
          </div>
        </div>
      </div>
    </div>

    <script>
      // 加载页面配置并显示页面列表
      async function loadPages() {
        try {
          const response = await fetch("/config/pages.json");
          const config = await response.json();
          const container = document.getElementById("pages-container");
          const pageCountEl = document.getElementById("page-count");

          if (config.pages && config.pages.length > 0) {
            pageCountEl.textContent = config.pages.length;

            const pagesGrid = document.createElement("div");
            pagesGrid.className = "pages-grid";

            config.pages.forEach((page) => {
              const pageCard = document.createElement("div");
              pageCard.className = "page-card";

              pageCard.innerHTML = `
                            <h3>${page.title}</h3>
                            <p>${page.description}</p>
                            <div class="meta">
                                <span>📄 ${page.id}</span>
                                <span>🔗 ${page.output}</span>
                            </div>
                            <div class="actions">
                                <a href="/pages/${page.id}/main.html" class="btn btn-outline" target="_blank">
                                    📝 预览Main
                                </a>
                                <a href="/pages/${page.id}/preview.html" class="btn btn-primary" target="_blank">
                                    🚀 完整预览
                                </a>
                                <button class="btn btn-outline" onclick="buildPage('${page.id}')">
                                    🔨 构建页面
                                </button>
                            </div>
                        `;

              pagesGrid.appendChild(pageCard);
            });

            container.appendChild(pagesGrid);
          } else {
            // 显示空状态
            const emptyState = document.createElement("div");
            emptyState.className = "empty-state";
            emptyState.innerHTML = `
                        <h3>🎯 开始创建您的第一个页面</h3>
                        <p>使用命令行工具快速创建新页面，享受模块化开发的便利</p>
                        <button class="btn" onclick="showCreateCommand()">
                            ➕ 创建新页面
                        </button>
                    `;
            container.appendChild(emptyState);
          }
        } catch (error) {
          console.error("加载页面配置失败:", error);
          const container = document.getElementById("pages-container");
          container.innerHTML = `
                    <div class="empty-state">
                        <h3>❌ 加载失败</h3>
                        <p>无法加载页面配置，请检查 src/config/pages.json 文件</p>
                    </div>
                `;
        }
      }

      // 构建单个页面
      function buildPage(pageId) {
        alert(`构建页面: ${pageId}\\n\\n请在终端运行: npm run build`);
      }

      // 显示创建命令
      function showCreateCommand() {
        alert('创建新页面:\\n\\nnpm run new-page <页面名称> [页面标题]\\n\\n例如:\\nnpm run new-page my-page "我的页面"');
      }

      // 页面加载时初始化
      document.addEventListener("DOMContentLoaded", function () {
        loadPages();
      });
    </script>
  </body>
</html>
