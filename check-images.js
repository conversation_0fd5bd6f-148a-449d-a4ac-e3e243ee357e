const fs = require('fs');
const https = require('https');
const http = require('http');

// 从test-accordion-swiper.html中提取的所有图片URL
const imageUrls = [
  'https://www.tomoviee.ai/images/home/<USER>',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-1.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-2.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-3.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-4.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-5.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-6.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-7.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-8.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-9.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-10.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-11.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-12.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-13.png',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-14.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-15.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-16.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-17.png',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-18.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-19.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-20.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-21.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-22.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-23.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-24.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-25.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-26.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-27.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-28.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-29.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-30.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-31.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-32.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-33.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-34.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-35.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-36.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-37.png',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-38.png',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-39.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-40.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-41.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-42.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-43.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-44.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-45.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-46.jpeg',
  'https://www.tomoviee.ai/images/home/<USER>/new-inspire-47.jpeg'
];

// 去重
const uniqueUrls = [...new Set(imageUrls)];

console.log(`开始检查 ${uniqueUrls.length} 个唯一图片链接...`);

// 检查单个URL的函数
function checkUrl(url) {
  return new Promise((resolve) => {
    const protocol = url.startsWith('https:') ? https : http;
    
    const req = protocol.get(url, (res) => {
      resolve({
        url: url,
        status: res.statusCode,
        statusText: res.statusMessage,
        is404: res.statusCode === 404,
        isError: res.statusCode >= 400
      });
    });

    req.on('error', (err) => {
      resolve({
        url: url,
        status: 'ERROR',
        statusText: err.message,
        is404: false,
        isError: true
      });
    });

    req.setTimeout(10000, () => {
      req.destroy();
      resolve({
        url: url,
        status: 'TIMEOUT',
        statusText: 'Request timeout',
        is404: false,
        isError: true
      });
    });
  });
}

// 批量检查所有URL
async function checkAllUrls() {
  const results = [];
  const errorUrls = [];
  const notFoundUrls = [];

  console.log('正在检查图片链接...\n');

  for (let i = 0; i < uniqueUrls.length; i++) {
    const url = uniqueUrls[i];
    console.log(`[${i + 1}/${uniqueUrls.length}] 检查: ${url}`);
    
    const result = await checkUrl(url);
    results.push(result);

    if (result.is404) {
      notFoundUrls.push(result);
      console.log(`❌ 404 NOT FOUND: ${url}`);
    } else if (result.isError) {
      errorUrls.push(result);
      console.log(`⚠️  ERROR (${result.status}): ${url} - ${result.statusText}`);
    } else {
      console.log(`✅ OK (${result.status}): ${url}`);
    }
  }

  // 输出总结
  console.log('\n=== 检查结果总结 ===');
  console.log(`总共检查: ${uniqueUrls.length} 个图片链接`);
  console.log(`正常访问: ${results.filter(r => !r.isError).length} 个`);
  console.log(`404错误: ${notFoundUrls.length} 个`);
  console.log(`其他错误: ${errorUrls.filter(r => !r.is404).length} 个`);

  if (notFoundUrls.length > 0) {
    console.log('\n=== 404错误的图片链接 ===');
    notFoundUrls.forEach((result, index) => {
      console.log(`${index + 1}. ${result.url}`);
    });
  }

  if (errorUrls.filter(r => !r.is404).length > 0) {
    console.log('\n=== 其他错误的图片链接 ===');
    errorUrls.filter(r => !r.is404).forEach((result, index) => {
      console.log(`${index + 1}. ${result.url} - ${result.status}: ${result.statusText}`);
    });
  }

  // 保存结果到文件
  const reportData = {
    checkTime: new Date().toISOString(),
    totalChecked: uniqueUrls.length,
    successCount: results.filter(r => !r.isError).length,
    notFoundCount: notFoundUrls.length,
    errorCount: errorUrls.filter(r => !r.is404).length,
    notFoundUrls: notFoundUrls.map(r => r.url),
    errorUrls: errorUrls.filter(r => !r.is404).map(r => ({url: r.url, status: r.status, message: r.statusText})),
    allResults: results
  };

  fs.writeFileSync('image-check-report.json', JSON.stringify(reportData, null, 2));
  console.log('\n详细报告已保存到: image-check-report.json');
}

// 运行检查
checkAllUrls().catch(console.error);
