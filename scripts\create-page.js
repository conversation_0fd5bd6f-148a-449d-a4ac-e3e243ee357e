const fs = require("fs");
const path = require("path");

// 生成预览页面模板的函数
function generatePreviewTemplate(pageId, title, description) {
  return `<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, shrink-to-fit=no"
    />
    <meta name="description" content="${description}" />
    <title>${title} - 开发预览</title>

    <!-- 可爱的预览页面Favicon 👀 -->
    <link rel="icon" type="image/svg+xml" href="/assets/favicons/eye-emoji.svg" />
    <link rel="shortcut icon" type="image/x-icon" href="/assets/favicons/eye-emoji.svg" />
    <link rel="apple-touch-icon" href="/assets/favicons/eye-emoji.svg" />

    <!-- 基础样式 -->
    <link rel="stylesheet" href="/shared/scss/_variables.scss" />
    <link rel="stylesheet" href="/shared/scss/_mixins.scss" />
    <link rel="stylesheet" href="/shared/scss/_base.scss" />
    <link rel="stylesheet" href="/shared/scss/_header.scss" />
    <link rel="stylesheet" href="/shared/scss/_footer.scss" />

    <!-- 页面专用样式 -->
    <link rel="stylesheet" href="/pages/${pageId}/style.scss" />

    <style>
      /* 开发预览提示条 */
      .dev-preview-bar {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        background: linear-gradient(90deg, #667eea, #764ba2);
        color: white;
        padding: 8px 20px;
        font-size: 14px;
        z-index: 9999;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .dev-preview-bar .info {
        display: flex;
        align-items: center;
        gap: 15px;
      }

      .dev-preview-bar .badge {
        background: rgba(255, 255, 255, 0.2);
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
      }

      .dev-preview-bar .actions {
        display: flex;
        gap: 10px;
      }

      .dev-preview-bar .btn-small {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: none;
        padding: 6px 12px;
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .dev-preview-bar .btn-small:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-1px);
      }

      /* 为开发预览条腾出空间 */
      body {
        padding-top: 50px;
      }

      @media (max-width: 768px) {
        .dev-preview-bar {
          flex-direction: column;
          gap: 10px;
          padding: 10px 15px;
        }

        body {
          padding-top: 80px;
        }
      }
    </style>
  </head>
  <body>
    <!-- 开发预览提示条 -->
    <div class="dev-preview-bar">
      <div class="info">
        <span>🚀 开发预览模式</span>
        <span class="badge">${pageId}</span>
        <span>${title}</span>
      </div>
      <div class="actions">
        <button class="btn-small" onclick="editMain()">📝 编辑Main</button>
        <button class="btn-small" onclick="editStyle()">🎨 编辑样式</button>
        <button class="btn-small" onclick="editScript()">⚡ 编辑脚本</button>
        <button class="btn-small" onclick="buildPage()">🔨 构建</button>
      </div>
    </div>

    <!-- 引入公共头部 -->
    <div id="header-placeholder"></div>

    <!-- 页面主要内容 -->
    <div id="main-placeholder"></div>

    <!-- 引入公共底部 -->
    <div id="footer-placeholder"></div>

    <!-- 公共脚本 -->
    <script type="module" src="/shared/js/common.js"></script>

    <!-- 页面专用脚本 -->
    <script type="module" src="/pages/${pageId}/script.js"></script>

    <script type="module">
      // 加载页面内容
      async function loadPageContent() {
        try {
          // 加载头部
          const headerResponse = await fetch("/templates/header.html");
          const headerContent = await headerResponse.text();
          document.getElementById("header-placeholder").innerHTML =
            headerContent;

          // 加载主要内容
          const mainResponse = await fetch("/pages/${pageId}/main.html");
          const mainContent = await mainResponse.text();
          document.getElementById("main-placeholder").innerHTML = mainContent;

          // 加载底部
          const footerResponse = await fetch("/templates/footer.html");
          const footerContent = await footerResponse.text();
          document.getElementById("footer-placeholder").innerHTML =
            footerContent;

          console.log("✅ 页面内容加载完成");
        } catch (error) {
          console.error("❌ 页面内容加载失败:", error);
          document.body.innerHTML += \`
                    <div style="padding: 40px; text-align: center; color: #e74c3c;">
                        <h2>⚠️ 页面加载失败</h2>
                        <p>请检查文件是否存在：</p>
                        <ul style="text-align: left; display: inline-block;">
                            <li>/pages/${pageId}/main.html</li>
                            <li>/pages/${pageId}/style.scss</li>
                            <li>/pages/${pageId}/script.js</li>
                        </ul>
                        <button onclick="location.reload()" style="margin-top: 20px; padding: 10px 20px; background: #3498db; color: white; border: none; border-radius: 4px; cursor: pointer;">
                            🔄 重新加载
                        </button>
                    </div>
                \`;
        }
      }

      // 开发工具函数
      window.editMain = () => {
        alert("请在编辑器中打开：\\\\nsrc/pages/${pageId}/main.html");
      };

      window.editStyle = () => {
        alert("请在编辑器中打开：\\\\nsrc/pages/${pageId}/style.scss");
      };

      window.editScript = () => {
        alert("请在编辑器中打开：\\\\nsrc/pages/${pageId}/script.js");
      };

      window.buildPage = () => {
        alert("请在终端运行：\\\\nnpm run build");
      };

      // 页面加载完成后加载内容
      document.addEventListener("DOMContentLoaded", loadPageContent);

      // 开发模式下的热更新支持
      console.log("🔥 热更新已启用");

      let lastMainContentHash = '';
      
      // 轮询检测main.html文件变化
      async function checkForUpdates() {
        try {
          const response = await fetch(\`/pages/${pageId}/main.html?t=\${Date.now()}\`);
          if (response.ok) {
            const content = await response.text();
            // 使用简单的字符串长度和前100字符作为hash
            const contentHash = content.length + '_' + content.substring(0, 100).replace(/\\s/g, '');
            
            if (lastMainContentHash && lastMainContentHash !== contentHash) {
              console.log("📥 检测到main.html内容变化");
              console.log("🔄 当前页面内容已更新，重新加载...");
              reloadMainContent();
            }
            
            lastMainContentHash = contentHash;
          }
        } catch (error) {
          console.log("❌ 轮询检测错误:", error);
        }
      }
      
      // 初始化内容hash - 延迟1秒开始，确保页面完全加载
      setTimeout(() => {
        checkForUpdates();
        // 每0.5秒检查一次文件变化
        setInterval(checkForUpdates, 500);
        console.log("📡 文件变化检测已启动 (0.5秒间隔)");
      }, 1000);

      // 重新加载主要内容的函数
      async function reloadMainContent() {
        try {
          console.log("🔄 重新加载主要内容...");
          const mainResponse = await fetch(
            "/pages/${pageId}/main.html?t=" + Date.now()
          );
          
          if (!mainResponse.ok) {
            throw new Error(\`HTTP \${mainResponse.status}: \${mainResponse.statusText}\`);
          }
          
          const mainContent = await mainResponse.text();
          console.log("📄 获取到新内容:", mainContent.substring(0, 100) + "...");
          
          const mainPlaceholder = document.getElementById("main-placeholder");
          if (!mainPlaceholder) {
            throw new Error("找不到 main-placeholder 元素");
          }
          
          // 保存滚动位置
          const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
          
          // 更新内容
          mainPlaceholder.innerHTML = mainContent;
          
          // 重新执行页面脚本
          const scripts = mainPlaceholder.querySelectorAll("script");
          scripts.forEach(script => {
            const newScript = document.createElement("script");
            if (script.src) {
              newScript.src = script.src;
            } else {
              newScript.textContent = script.textContent;
            }
            script.parentNode.replaceChild(newScript, script);
          });
          
          // 恢复滚动位置
          window.scrollTo(0, scrollTop);
          
          console.log("✅ 主要内容重新加载完成");

          // 显示更新提示
          showUpdateNotification();
        } catch (error) {
          console.error("❌ 重新加载主要内容失败:", error);
          // 显示错误提示
          showErrorNotification(error.message);
        }
      }

      // 显示更新提示
      function showUpdateNotification() {
        // 创建更新提示
        const notification = document.createElement("div");
        notification.style.cssText = \`
          position: fixed;
          top: 60px;
          right: 20px;
          background: #27ae60;
          color: white;
          padding: 12px 20px;
          border-radius: 6px;
          box-shadow: 0 4px 12px rgba(0,0,0,0.2);
          z-index: 10000;
          font-size: 14px;
          opacity: 0;
          transform: translateX(100%);
          transition: all 0.3s ease;
        \`;
        notification.innerHTML = "✅ 页面内容已更新";
        document.body.appendChild(notification);

        // 显示动画
        requestAnimationFrame(() => {
          notification.style.opacity = "1";
          notification.style.transform = "translateX(0)";
        });

        // 3秒后自动消失
        setTimeout(() => {
          notification.style.opacity = "0";
          notification.style.transform = "translateX(100%)";
          setTimeout(() => {
            if (document.body.contains(notification)) {
              document.body.removeChild(notification);
            }
          }, 300);
        }, 3000);
      }

      // 显示错误提示
      function showErrorNotification(errorMessage) {
        // 创建错误提示
        const notification = document.createElement("div");
        notification.style.cssText = \`
          position: fixed;
          top: 60px;
          right: 20px;
          background: #e74c3c;
          color: white;
          padding: 12px 20px;
          border-radius: 6px;
          box-shadow: 0 4px 12px rgba(0,0,0,0.2);
          z-index: 10000;
          font-size: 14px;
          opacity: 0;
          transform: translateX(100%);
          transition: all 0.3s ease;
          max-width: 300px;
        \`;
        notification.innerHTML = \`❌ 更新失败: \${errorMessage}\`;
        document.body.appendChild(notification);

        // 显示动画
        requestAnimationFrame(() => {
          notification.style.opacity = "1";
          notification.style.transform = "translateX(0)";
        });

        // 5秒后自动消失
        setTimeout(() => {
          notification.style.opacity = "0";
          notification.style.transform = "translateX(100%)";
          setTimeout(() => {
            if (document.body.contains(notification)) {
              document.body.removeChild(notification);
            }
          }, 300);
        }, 5000);
      }
    </script>
  </body>
</html>`;
}

function createPage() {
  const pageName = process.argv[2];
  const pageTitle = process.argv[3] || pageName;

  if (!pageName) {
    console.error("❌ 使用方法: npm run new-page <页面名称> [页面标题]");
    console.error('   例如: npm run new-page my-page "我的页面"');
    process.exit(1);
  }

  // 验证页面名称格式
  if (!/^[a-z0-9-]+$/.test(pageName)) {
    console.error("❌ 页面名称只能包含小写字母、数字和连字符");
    process.exit(1);
  }

  const pageDir = path.join("src/pages", pageName);

  // 检查页面是否已存在
  if (fs.existsSync(pageDir)) {
    console.error(`❌ 页面 "${pageName}" 已存在`);
    process.exit(1);
  }

  // 创建页面目录
  fs.mkdirSync(pageDir, { recursive: true });

  // 创建assets目录结构
  const assetsDir = path.join(pageDir, "assets");
  fs.mkdirSync(path.join(assetsDir, "images"), { recursive: true });
  fs.mkdirSync(path.join(assetsDir, "videos"), { recursive: true });
  fs.mkdirSync(path.join(assetsDir, "files"), { recursive: true });

  // 创建main.html模板
  const mainTemplate = `<main class="main-content">
    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <div class="hero-text">
                    <h1>${pageTitle}</h1>
                    <p class="hero-subtitle">
                        在这里编写您的页面介绍文本...
                    </p>
                    <div class="hero-buttons">
                        <button class="btn btn-primary" onclick="handlePrimaryAction()">
                            主要操作
                        </button>
                        <button class="btn btn-outline" onclick="learnMore()">
                            了解更多
                        </button>
                    </div>
                </div>
                <div class="hero-image">
                    <div class="placeholder-image">
                        <p>Hero Image</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Content Section -->
    <section class="content">
        <div class="container">
            <div class="content-grid">
                <div class="content-text">
                    <h2>内容标题</h2>
                    <p>在这里添加您的内容...</p>
                    <ul>
                        <li>功能特点 1</li>
                        <li>功能特点 2</li>
                        <li>功能特点 3</li>
                    </ul>
                    <button class="btn btn-secondary" onclick="handleAction()">
                        操作按钮
                    </button>
                </div>
                <div class="content-image">
                    <div class="placeholder-image">
                        <p>Content Image</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta">
        <div class="container">
            <div class="cta-content">
                <h2>准备开始了吗？</h2>
                <p>在这里添加号召用户行动的文本</p>
                <div class="cta-buttons">
                    <button class="btn btn-cta-primary" onclick="getStarted()">
                        立即开始
                    </button>
                </div>
            </div>
        </div>
    </section>
</main>`;

  // 创建SCSS模板
  const scssTemplate = `@import '../../shared/scss/variables';
@import '../../shared/scss/mixins';

// ${pageTitle} 页面样式
.main-content {
    // Hero Section
    .hero {
        @include gradient-bg();
        color: white;
        padding: 120px 0;
        
        .hero-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: center;
            
            @include respond-to('tablet') {
                grid-template-columns: 1fr;
                text-align: center;
            }
        }
        
        .hero-text {
            h1 {
                font-size: 3rem;
                font-weight: 700;
                margin-bottom: 20px;
                
                @include respond-to('mobile') {
                    font-size: 2.5rem;
                }
            }
            
            .hero-subtitle {
                font-size: 1.2rem;
                margin-bottom: 40px;
                opacity: 0.9;
                line-height: 1.6;
            }
            
            .hero-buttons {
                display: flex;
                gap: 20px;
                
                @include respond-to('mobile') {
                    flex-direction: column;
                    align-items: center;
                }
            }
        }
        
        .hero-image {
            .placeholder-image {
                width: 100%;
                height: 300px;
                background: rgba(255,255,255,0.1);
                border-radius: $large-border-radius;
                @include flex-center;
                font-size: 1.5rem;
                color: rgba(255,255,255,0.7);
                border: 2px dashed rgba(255,255,255,0.3);
            }
        }
    }
    
    // Content Section
    .content {
        padding: 100px 0;
        
        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: center;
            
            @include respond-to('tablet') {
                grid-template-columns: 1fr;
            }
        }
        
        .content-text {
            h2 {
                font-size: $font-size-xxl;
                color: $text-color;
                margin-bottom: 20px;
                font-weight: 700;
            }
            
            p {
                font-size: $font-size-lg;
                color: $light-text-color;
                margin-bottom: 20px;
                line-height: 1.6;
            }
            
            ul {
                margin-bottom: 30px;
                
                li {
                    color: $text-color;
                    margin-bottom: 10px;
                    padding-left: 20px;
                    position: relative;
                    
                    &:before {
                        content: "✓";
                        position: absolute;
                        left: 0;
                        color: $primary-color;
                        font-weight: bold;
                    }
                }
            }
        }
        
        .content-image {
            .placeholder-image {
                width: 100%;
                height: 300px;
                background: $light-bg-color;
                border-radius: $large-border-radius;
                @include flex-center;
                font-size: 1.5rem;
                color: #999;
                border: 2px dashed #ddd;
            }
        }
    }
    
    // CTA Section
    .cta {
        @include gradient-bg();
        color: white;
        padding: 100px 0;
        text-align: center;
        
        .cta-content {
            max-width: 600px;
            margin: 0 auto;
            
            h2 {
                font-size: $font-size-xxl;
                margin-bottom: 20px;
                font-weight: 700;
            }
            
            p {
                font-size: $font-size-lg;
                margin-bottom: 40px;
                opacity: 0.9;
                line-height: 1.6;
            }
            
            .cta-buttons {
                display: flex;
                gap: 20px;
                justify-content: center;
                
                @include respond-to('mobile') {
                    flex-direction: column;
                    align-items: center;
                }
            }
        }
    }
}

// 按钮样式
.btn {
    @include btn-base;
    
    &.btn-primary {
        background: $accent-color;
        color: white;
        
        &:hover {
            background: darken($accent-color, 10%);
        }
    }
    
    &.btn-outline {
        background: transparent;
        color: white;
        border: 2px solid white;
        
        &:hover {
            background: white;
            color: $primary-color;
        }
    }
    
    &.btn-secondary {
        background: $primary-color;
        color: white;
        
        &:hover {
            background: darken($primary-color, 10%);
        }
    }
    
    &.btn-cta-primary {
        background: $accent-color;
        color: white;
        padding: 20px 40px;
        font-size: $font-size-lg;
        
        &:hover {
            background: darken($accent-color, 10%);
            transform: translateY(-3px);
            @include shadow(3);
        }
    }
}`;

  // 创建JS模板
  const jsTemplate = `// ${pageTitle} 页面交互逻辑

// 主要操作按钮
function handlePrimaryAction() {
    console.log('Primary action clicked for ${pageName}');
    alert('主要操作被点击！');
}

// 了解更多
function learnMore() {
    console.log('Learn more clicked');
    // 平滑滚动到内容区域
    smoothScrollTo('.content');
}

// 处理操作
function handleAction() {
    console.log('Handle action clicked');
    alert('操作执行成功！');
}

// 立即开始
function getStarted() {
    console.log('Get started clicked');
    alert('立即开始功能！');
}

// 页面特定的初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('${pageTitle} page loaded');
    
    // 添加页面特定的初始化逻辑
    initPageAnimations();
});

// 初始化页面动画
function initPageAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);
    
    // 观察需要动画的元素
    document.querySelectorAll('.content-text, .cta-content').forEach(element => {
        observer.observe(element);
    });
}`;

  // 复制预览页面模板
  const previewTemplate = fs.readFileSync("src/templates/page-preview.html", "utf8");

  // 写入文件
  fs.writeFileSync(path.join(pageDir, "main.html"), mainTemplate);
  fs.writeFileSync(path.join(pageDir, "style.scss"), scssTemplate);
  fs.writeFileSync(path.join(pageDir, "script.js"), jsTemplate);
  fs.writeFileSync(path.join(pageDir, "preview.html"), previewTemplate);

  // 更新页面配置
  const configPath = "src/config/pages.json";
  const config = JSON.parse(fs.readFileSync(configPath, "utf8"));

  const pageConfig = {
    id: pageName,
    title: pageTitle,
    description: `${pageTitle} - 在这里添加页面描述`,
    output: `${pageName}.html`,
    cdnStyles: [
      // 根据需要添加CDN样式文件
    ],
    cdnScripts: [
      // 根据需要添加CDN脚本文件
    ],
    assetsConfig: {
      baseUrl: `https://cdn.example.com/assets/${pageName}/`,
      videoBaseUrl: `https://cdn.example.com/assets/${pageName}/`,
      supportedExtensions: [".jpg", ".jpeg", ".png", ".gif", ".svg", ".webp", ".mp4", ".webm", ".ogg", ".avi", ".mov", ".pdf", ".doc", ".docx", ".zip", ".rar"],
      videoExtensions: [".mp4", ".webm", ".ogg", ".avi", ".mov"],
    },
  };

  config.pages.push(pageConfig);

  fs.writeFileSync(configPath, JSON.stringify(config, null, 2));

  console.log(`✅ 页面 "${pageName}" 创建成功！`);
  console.log(`📁 位置: ${pageDir}`);
  console.log(`📝 文件:`);
  console.log(`   - main.html (main区域内容)`);
  console.log(`   - style.scss (页面样式)`);
  console.log(`   - script.js (页面交互)`);
  console.log(`📁 资源目录:`);
  console.log(`   - assets/images/ (图片资源)`);
  console.log(`   - assets/videos/ (视频资源)`);
  console.log(`   - assets/files/ (文档资源)`);
  console.log(`⚙️  已更新配置: ${configPath}`);
  console.log(`🚀 运行以下命令:`);
  console.log(`   npm run dev    # 开发模式`);
  console.log(`   npm run build  # 构建页面`);
}

// 执行创建页面
if (require.main === module) {
  createPage();
}

module.exports = createPage;
