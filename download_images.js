#!/usr/bin/env node
/**
 * 批量下载图片脚本 (Node.js版本)
 * 从temp2.html文件中读取图片链接，按顺序下载并重命名为1.jpg、2.jpg等
 */

const fs = require("fs");
const path = require("path");
const https = require("https");
const http = require("http");
const { URL } = require("url");

/**
 * 从文件中读取所有图片URL
 * @param {string} filename 文件名
 * @returns {string[]} URL数组
 */
function readUrlsFromFile(filename) {
  try {
    const content = fs.readFileSync(filename, "utf-8");
    // 使用正则表达式提取所有https://开头的URL
    const urlPattern = /https:\/\/[^\s\n]+/g;
    const urls = content.match(urlPattern) || [];
    return urls;
  } catch (error) {
    if (error.code === "ENOENT") {
      console.log(`错误：找不到文件 ${filename}`);
    } else {
      console.log(`读取文件时出错：${error.message}`);
    }
    return [];
  }
}

/**
 * 从URL中获取文件扩展名
 * @param {string} url 图片URL
 * @returns {string} 文件扩展名
 */
function getFileExtension(url) {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    if (pathname.includes(".")) {
      return pathname.split(".").pop().toLowerCase();
    }
    return "jpg"; // 默认扩展名
  } catch {
    return "jpg";
  }
}

/**
 * 下载单个图片
 * @param {string} url 图片URL
 * @param {string} filename 保存的文件名
 * @param {number} maxRetries 最大重试次数
 * @returns {Promise<boolean>} 下载是否成功
 */
function downloadImage(url, filename, maxRetries = 3) {
  return new Promise((resolve) => {
    const attemptDownload = (attempt) => {
      console.log(`正在下载: ${filename} (尝试 ${attempt}/${maxRetries})`);

      const urlObj = new URL(url);
      const client = urlObj.protocol === "https:" ? https : http;

      const options = {
        headers: {
          "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        },
        timeout: 30000,
      };

      const req = client.get(url, options, (res) => {
        if (res.statusCode !== 200) {
          console.log(`✗ HTTP错误 ${filename}: ${res.statusCode}`);
          if (attempt < maxRetries) {
            console.log("等待2秒后重试...");
            setTimeout(() => attemptDownload(attempt + 1), 2000);
          } else {
            console.log(`✗ 最终下载失败: ${filename}`);
            resolve(false);
          }
          return;
        }

        const fileStream = fs.createWriteStream(filename);
        res.pipe(fileStream);

        fileStream.on("finish", () => {
          fileStream.close();
          console.log(`✓ 成功下载: ${filename}`);
          resolve(true);
        });

        fileStream.on("error", (err) => {
          console.log(`✗ 文件写入错误 ${filename}: ${err.message}`);
          if (attempt < maxRetries) {
            console.log("等待2秒后重试...");
            setTimeout(() => attemptDownload(attempt + 1), 2000);
          } else {
            console.log(`✗ 最终下载失败: ${filename}`);
            resolve(false);
          }
        });
      });

      req.on("error", (err) => {
        console.log(`✗ 请求错误 ${filename}: ${err.message}`);
        if (attempt < maxRetries) {
          console.log("等待2秒后重试...");
          setTimeout(() => attemptDownload(attempt + 1), 2000);
        } else {
          console.log(`✗ 最终下载失败: ${filename}`);
          resolve(false);
        }
      });

      req.on("timeout", () => {
        req.destroy();
        console.log(`✗ 请求超时 ${filename}`);
        if (attempt < maxRetries) {
          console.log("等待2秒后重试...");
          setTimeout(() => attemptDownload(attempt + 1), 2000);
        } else {
          console.log(`✗ 最终下载失败: ${filename}`);
          resolve(false);
        }
      });
    };

    attemptDownload(1);
  });
}

/**
 * 延迟函数
 * @param {number} ms 延迟毫秒数
 * @returns {Promise<void>}
 */
function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

/**
 * 主函数
 */
async function main() {
  // 输入文件名
  const inputFile = "temp2.html";

  // 创建下载目录
  const downloadDir = "downloaded_images";
  if (!fs.existsSync(downloadDir)) {
    fs.mkdirSync(downloadDir, { recursive: true });
    console.log(`创建下载目录: ${downloadDir}`);
  }

  // 读取URL列表
  console.log(`正在从 ${inputFile} 读取图片链接...`);
  const urls = readUrlsFromFile(inputFile);

  if (urls.length === 0) {
    console.log("没有找到任何图片链接！");
    return;
  }

  console.log(`找到 ${urls.length} 个图片链接`);
  console.log(`将按原始顺序下载所有图片（包含重复链接）`);

  // 下载图片
  let successCount = 0;
  let failedCount = 0;

  for (let i = 0; i < urls.length; i++) {
    const url = urls[i];
    const ext = getFileExtension(url);
    const filename = path.join(downloadDir, `${i + 1}.${ext}`);

    // 如果文件已存在，跳过
    if (fs.existsSync(filename)) {
      console.log(`文件已存在，跳过: ${filename}`);
      continue;
    }

    // 下载图片
    const success = await downloadImage(url, filename);
    if (success) {
      successCount++;
    } else {
      failedCount++;
    }

    // 添加延迟避免请求过于频繁
    await sleep(500);
  }

  // 输出统计信息
  console.log("\n" + "=".repeat(50));
  console.log("下载完成！");
  console.log(`成功下载: ${successCount} 个文件`);
  console.log(`下载失败: ${failedCount} 个文件`);
  console.log(`文件保存在: ${downloadDir} 目录`);
  console.log("=".repeat(50));
}

// 运行主函数
if (require.main === module) {
  main().catch(console.error);
}
