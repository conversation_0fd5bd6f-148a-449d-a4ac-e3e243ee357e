// 动态预览页面生成器
// 根据pages.json配置生成准确的预览页面

class PreviewGenerator {
  constructor() {
    this.pagesConfig = null;
    this.currentPageId = null;
    this.currentPageConfig = null;
  }

  // 从URL参数获取页面ID
  getCurrentPageId() {
    const pathSegments = window.location.pathname.split("/");
    const pagesIndex = pathSegments.indexOf("pages");
    if (pagesIndex !== -1 && pathSegments[pagesIndex + 1]) {
      return pathSegments[pagesIndex + 1];
    }
    return null;
  }

  // 加载页面配置
  async loadPagesConfig() {
    try {
      const response = await fetch("/config/pages.json");
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      this.pagesConfig = await response.json();
      return this.pagesConfig;
    } catch (error) {
      console.error("❌ 加载页面配置失败:", error);
      throw error;
    }
  }

  // 获取当前页面配置
  getCurrentPageConfig() {
    if (!this.pagesConfig || !this.currentPageId) {
      return null;
    }
    return this.pagesConfig.pages.find((page) => page.id === this.currentPageId);
  }

  // 动态加载CDN样式
  async loadCdnStyles(styles) {
    if (!styles || !Array.isArray(styles)) return;

    const promises = styles.map((styleUrl) => {
      return new Promise((resolve, reject) => {
        // 检查是否已经加载
        if (document.querySelector(`link[href="${styleUrl}"]`)) {
          resolve();
          return;
        }

        const link = document.createElement("link");
        link.rel = "stylesheet";
        link.href = styleUrl;
        link.onload = () => {
          console.log(`✅ CDN样式加载成功: ${styleUrl}`);
          resolve();
        };
        link.onerror = () => {
          console.warn(`⚠️ CDN样式加载失败: ${styleUrl}`);
          reject(new Error(`Failed to load CSS: ${styleUrl}`));
        };
        document.head.appendChild(link);
      });
    });

    try {
      await Promise.all(promises);
      console.log("✅ 所有CDN样式加载完成");
    } catch (error) {
      console.warn("⚠️ 部分CDN样式加载失败:", error);
    }
  }

  // 动态加载CDN脚本
  async loadCdnScripts(scripts) {
    if (!scripts || !Array.isArray(scripts)) return;

    // 按顺序加载脚本（避免依赖问题）
    for (const scriptUrl of scripts) {
      await new Promise((resolve, reject) => {
        // 检查是否已经加载
        if (document.querySelector(`script[src="${scriptUrl}"]`)) {
          resolve();
          return;
        }

        const script = document.createElement("script");
        script.src = scriptUrl;
        script.onload = () => {
          console.log(`✅ CDN脚本加载成功: ${scriptUrl}`);
          resolve();
        };
        script.onerror = () => {
          console.warn(`⚠️ CDN脚本加载失败: ${scriptUrl}`);
          reject(new Error(`Failed to load script: ${scriptUrl}`));
        };
        document.head.appendChild(script);
      });
    }

    console.log("✅ 所有CDN脚本加载完成");
  }

  // 加载页面专用脚本
  async loadPageScript() {
    if (!this.currentPageId) return;

    const scriptUrl = `/pages/${this.currentPageId}/script.js`;

    try {
      // 检查脚本文件是否存在
      const response = await fetch(scriptUrl);
      if (!response.ok) {
        console.warn(`⚠️ 页面脚本不存在: ${scriptUrl}`);
        return;
      }

      // 检查是否已经加载
      const existingScript = document.querySelector(`script[src="${scriptUrl}"]`);
      if (existingScript) {
        console.log(`✅ 页面脚本已存在: ${scriptUrl}`);
        return;
      }

      // 等待jQuery加载完成
      await this.waitForJQuery();

      // 创建并加载脚本
      await new Promise((resolve, reject) => {
        const script = document.createElement("script");
        script.src = scriptUrl;
        script.type = "module";
        script.onload = () => {
          console.log(`✅ 页面脚本加载成功: ${scriptUrl}`);
          resolve();
        };
        script.onerror = () => {
          console.warn(`⚠️ 页面脚本加载失败: ${scriptUrl}`);
          reject(new Error(`Failed to load page script: ${scriptUrl}`));
        };
        document.head.appendChild(script);
      });
    } catch (error) {
      console.warn(`⚠️ 加载页面脚本时出错: ${error.message}`);
    }
  }

  // 等待jQuery加载完成
  async waitForJQuery() {
    return new Promise((resolve) => {
      const checkJQuery = () => {
        if (typeof window.$ !== "undefined" && typeof window.jQuery !== "undefined") {
          console.log("✅ jQuery已加载完成");
          resolve();
        } else {
          setTimeout(checkJQuery, 50); // 每50ms检查一次
        }
      };
      checkJQuery();
    });
  }

  // 重新初始化页面脚本 (用于热更新后重新绑定事件)
  async reinitializePageScript() {
    if (!this.currentPageId) return;

    try {
      const scriptUrl = `/pages/${this.currentPageId}/script.js`;

      // 移除现有的页面脚本
      const existingScript = document.querySelector(`script[src^="${scriptUrl}"]`);
      if (existingScript) {
        existingScript.remove();
        console.log("🧹 已移除旧的页面脚本");
      }

      // 等待一小段时间确保脚本完全移除
      await new Promise((resolve) => setTimeout(resolve, 100));

      // 等待jQuery加载完成
      await this.waitForJQuery();

      // 重新加载页面脚本
      await new Promise((resolve, reject) => {
        const script = document.createElement("script");
        script.src = `${scriptUrl}?t=${Date.now()}`;
        script.type = "module";
        script.onload = () => {
          console.log("✅ 页面脚本重新初始化成功");
          resolve();
        };
        script.onerror = () => {
          console.warn("⚠️ 页面脚本重新初始化失败");
          reject(new Error(`Failed to reinitialize page script: ${scriptUrl}`));
        };
        document.head.appendChild(script);
      });
    } catch (error) {
      console.warn(`⚠️ 重新初始化页面脚本时出错: ${error.message}`);
    }
  }

  // 处理资源文件路径转换
  processAssetUrls(content, pageId, assetsConfig) {
    if (!content || !assetsConfig) return content;

    const { baseUrl, supportedExtensions } = assetsConfig;

    // 构建正则表达式匹配资源文件
    const extensionPattern = supportedExtensions.map((ext) => ext.replace(".", "\\.")).join("|");

    // 匹配 assets/ 开头的资源文件路径
    const assetRegex = new RegExp(`(src|href)=["']assets/([^"']*\\.(${extensionPattern}))["']`, "gi");

    return content.replace(assetRegex, (match, attr, assetPath, ext) => {
      // 移除 assetPath 开头的 "images/" 前缀，避免重复
      const cleanAssetPath = assetPath.startsWith("images/") ? assetPath.substring(7) : assetPath;

      // 开发环境：使用相对路径
      if (window.location.hostname === "localhost" || window.location.hostname === "127.0.0.1") {
        return `${attr}="/pages/${pageId}/assets/${assetPath}"`;
      }
      // 生产环境：使用CDN路径
      else {
        return `${attr}="${baseUrl}${cleanAssetPath}"`;
      }
    });
  }

  // 更新页面标题和meta信息
  updatePageMeta(pageConfig) {
    if (!pageConfig) return;

    // 更新标题
    document.title = `${pageConfig.title} - 开发预览`;

    // 更新description
    let descriptionMeta = document.querySelector('meta[name="description"]');
    if (descriptionMeta) {
      descriptionMeta.content = pageConfig.description;
    }

    // 更新favicon 🌟
    this.updateFavicon(pageConfig.favicon);

    // 更新预览条信息
    const badge = document.querySelector(".dev-preview-bar .badge");
    const titleSpan = document.querySelector(".dev-preview-bar .info span:last-child");

    if (badge) badge.textContent = pageConfig.id;
    if (titleSpan) titleSpan.textContent = pageConfig.title;
  }

  // 动态更新favicon
  updateFavicon(faviconPath) {
    if (!faviconPath) {
      // 如果没有指定favicon，使用默认的眼睛图标（适合预览页面）
      faviconPath = "/assets/favicons/eye-emoji.svg";
    }

    // 移除现有的favicon链接
    const existingFavicons = document.querySelectorAll('link[rel*="icon"]');
    existingFavicons.forEach((link) => link.remove());

    // 添加新的favicon链接
    const faviconLinks = [{ rel: "icon", type: "image/svg+xml" }, { rel: "shortcut icon", type: "image/x-icon" }, { rel: "apple-touch-icon" }];

    faviconLinks.forEach(({ rel, type }) => {
      const link = document.createElement("link");
      link.rel = rel;
      if (type) link.type = type;
      link.href = faviconPath;
      document.head.appendChild(link);
    });

    console.log(`✅ Favicon已更新: ${faviconPath}`);
  }

  // 加载页面内容
  async loadPageContent() {
    try {
      // 清理之前可能残留的样式和脚本
      this.cleanupPreviousContent();

      // 加载头部内容并正确处理script/style标签
      const headerResponse = await fetch("/templates/header.html");
      const headerContent = await headerResponse.text();
      this.insertContentWithScriptsAndStyles(headerContent, "header-placeholder");

      // 加载主要内容
      const mainResponse = await fetch(`/pages/${this.currentPageId}/main.html`);
      let mainContent = await mainResponse.text();

      // 处理资源文件路径
      if (this.currentPageConfig && this.currentPageConfig.assetsConfig) {
        mainContent = this.processAssetUrls(mainContent, this.currentPageId, this.currentPageConfig.assetsConfig);
      }

      // 主要内容可以直接使用innerHTML，因为通常不包含需要执行的script
      document.getElementById("main-placeholder").innerHTML = mainContent;

      // 加载底部内容并正确处理script/style标签
      const footerResponse = await fetch("/templates/footer.html");
      const footerContent = await footerResponse.text();
      this.insertContentWithScriptsAndStyles(footerContent, "footer-placeholder");

      console.log("✅ 页面内容加载完成");
    } catch (error) {
      console.error("❌ 页面内容加载失败:", error);
      this.showLoadError(error);
    }
  }

  // 清理之前加载的样式和脚本
  cleanupPreviousContent() {
    // 清理之前动态添加的样式
    const existingStyles = document.querySelectorAll("style[data-source]");
    existingStyles.forEach((style) => {
      style.remove();
    });

    // 清理之前动态添加的脚本（注意：已执行的脚本无法撤销其效果）
    const existingScripts = document.querySelectorAll("script[data-source]");
    existingScripts.forEach((script) => {
      script.remove();
    });

    console.log("🧹 清理了之前的动态样式和脚本");
  }

  // 插入包含script和style的内容并确保它们正确执行
  insertContentWithScriptsAndStyles(content, placeholderId) {
    const placeholder = document.getElementById(placeholderId);
    if (!placeholder) {
      console.error(`找不到占位符元素: ${placeholderId}`);
      return;
    }

    // 创建临时容器来解析内容
    const tempDiv = document.createElement("div");
    tempDiv.innerHTML = content;

    // 提取并处理style标签
    const styleElements = tempDiv.querySelectorAll("style");
    styleElements.forEach((style) => {
      // 创建新的style元素并添加到head中
      const newStyle = document.createElement("style");
      newStyle.textContent = style.textContent;
      newStyle.setAttribute("data-source", placeholderId);
      document.head.appendChild(newStyle);

      // 从临时容器中移除，避免重复
      style.remove();
    });

    // 提取并处理script标签
    const scriptElements = tempDiv.querySelectorAll("script");
    const scriptPromises = [];

    scriptElements.forEach((script) => {
      const promise = new Promise((resolve, reject) => {
        const newScript = document.createElement("script");
        newScript.setAttribute("data-source", placeholderId);

        if (script.src) {
          // 外部脚本
          newScript.src = script.src;
          newScript.onload = resolve;
          newScript.onerror = reject;
          if (script.defer) newScript.defer = true;
          if (script.async) newScript.async = true;
        } else {
          // 内联脚本
          newScript.textContent = script.textContent;
          // 内联脚本会立即执行
          setTimeout(resolve, 0);
        }

        document.head.appendChild(newScript);
      });

      scriptPromises.push(promise);
      script.remove(); // 从临时容器中移除
    });

    // 将剩余的HTML内容插入到占位符中
    placeholder.innerHTML = tempDiv.innerHTML;

    // 等待所有脚本加载完成
    Promise.all(scriptPromises)
      .then(() => {
        console.log(`✅ ${placeholderId} 的脚本和样式加载完成`);
      })
      .catch((error) => {
        console.warn(`⚠️ ${placeholderId} 部分脚本加载失败:`, error);
      });
  }

  // 显示加载错误
  showLoadError(error) {
    document.body.innerHTML += `
      <div style="padding: 40px; text-align: center; color: #e74c3c; background: #fff; margin: 20px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">
        <h2>⚠️ 页面加载失败</h2>
        <p style="margin: 20px 0;">错误信息: ${error.message}</p>
        <p>请检查文件是否存在：</p>
        <ul style="text-align: left; display: inline-block; margin: 20px 0;">
          <li>/pages/${this.currentPageId}/main.html</li>
          <li>/pages/${this.currentPageId}/style.scss</li>
          <li>/pages/${this.currentPageId}/script.js</li>
          <li>/config/pages.json</li>
        </ul>
        <button onclick="location.reload()" style="margin-top: 20px; padding: 10px 20px; background: #3498db; color: white; border: none; border-radius: 4px; cursor: pointer;">
          🔄 重新加载
        </button>
      </div>
    `;
  }

  // 初始化预览页面
  async init() {
    try {
      // 获取当前页面ID
      this.currentPageId = this.getCurrentPageId();
      if (!this.currentPageId) {
        throw new Error("无法获取页面ID");
      }

      console.log(`🚀 初始化预览页面: ${this.currentPageId}`);

      // 加载页面配置
      await this.loadPagesConfig();
      this.currentPageConfig = this.getCurrentPageConfig();

      if (!this.currentPageConfig) {
        throw new Error(`页面配置不存在: ${this.currentPageId}`);
      }

      console.log("📋 页面配置:", this.currentPageConfig);

      // 更新页面meta信息
      this.updatePageMeta(this.currentPageConfig);

      // 加载CDN样式（优先加载）
      if (this.currentPageConfig.cdnStyles && this.currentPageConfig.cdnStyles.length > 0) {
        console.log("📥 加载CDN样式...");
        await this.loadCdnStyles(this.currentPageConfig.cdnStyles);
      }

      // 加载页面内容
      await this.loadPageContent();

      // 加载CDN脚本（在内容加载后）
      if (this.currentPageConfig.cdnScripts && this.currentPageConfig.cdnScripts.length > 0) {
        console.log("📥 加载CDN脚本...");
        await this.loadCdnScripts(this.currentPageConfig.cdnScripts);
      }

      // 加载页面专用脚本
      await this.loadPageScript();

      console.log("🎉 预览页面初始化完成");

      // 设置热更新
      this.setupHotReload();
    } catch (error) {
      console.error("❌ 预览页面初始化失败:", error);
      this.showLoadError(error);
    }
  }

  // 设置热更新
  setupHotReload() {
    console.log("🔥 热更新已启用");

    let lastMainContentHash = "";
    let lastStyleContentHash = "";
    let lastScriptContentHash = "";

    // 轮询检测文件变化
    const checkForUpdates = async () => {
      try {
        // 检测main.html变化
        const mainResponse = await fetch(`/pages/${this.currentPageId}/main.html?t=${Date.now()}`);
        if (mainResponse.ok) {
          const content = await mainResponse.text();
          const contentHash = this.generateContentHash(content);

          if (lastMainContentHash && lastMainContentHash !== contentHash) {
            console.log("📥 检测到main.html内容变化");
            console.log(`   旧hash: ${lastMainContentHash.substring(0, 20)}...`);
            console.log(`   新hash: ${contentHash.substring(0, 20)}...`);
            await this.reloadMainContent();
          }

          lastMainContentHash = contentHash;
        }

        // 检测style.scss变化
        const styleResponse = await fetch(`/pages/${this.currentPageId}/style.scss?t=${Date.now()}`);
        if (styleResponse.ok) {
          const styleContent = await styleResponse.text();
          const styleHash = this.generateContentHash(styleContent);

          if (lastStyleContentHash && lastStyleContentHash !== styleHash) {
            console.log("🎨 检测到style.scss内容变化");
            console.log(`   旧hash: ${lastStyleContentHash.substring(0, 20)}...`);
            console.log(`   新hash: ${styleHash.substring(0, 20)}...`);
            await this.reloadStyles();
          }

          lastStyleContentHash = styleHash;
        }

        // 检测script.js变化
        const scriptResponse = await fetch(`/pages/${this.currentPageId}/script.js?t=${Date.now()}`);
        if (scriptResponse.ok) {
          const scriptContent = await scriptResponse.text();
          const scriptHash = this.generateContentHash(scriptContent);

          if (lastScriptContentHash && lastScriptContentHash !== scriptHash) {
            console.log("📜 检测到script.js内容变化");
            console.log(`   旧hash: ${lastScriptContentHash.substring(0, 20)}...`);
            console.log(`   新hash: ${scriptHash.substring(0, 20)}...`);
            await this.reloadPageScript();
          }

          lastScriptContentHash = scriptHash;
        }
      } catch (error) {
        console.log("❌ 轮询检测错误:", error);
      }
    };

    // 初始化内容hash - 延迟1秒开始，确保页面完全加载
    setTimeout(() => {
      checkForUpdates();
      // 每0.5秒检查一次文件变化
      setInterval(checkForUpdates, 500);
      console.log("📡 文件变化检测已启动 (0.5秒间隔)");
      console.log("   - 监听 main.html 变化");
      console.log("   - 监听 style.scss 变化");
      console.log("   - 监听 script.js 变化");
    }, 1000);
  }

  // 生成内容hash - 使用简单但有效的方法
  generateContentHash(content) {
    // 方法1：使用内容长度 + 多个位置的采样
    let hash = content.length + "_";

    // 从内容的不同位置采样
    const sampleSize = 50;
    const samples = 5;
    const step = Math.floor(content.length / samples);

    for (let i = 0; i < samples; i++) {
      const start = i * step;
      const sample = content.substring(start, start + sampleSize).replace(/\s/g, "");
      hash += sample;
    }

    // 方法2：简单的字符串hash函数（如果需要更短的hash）
    let numericHash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      numericHash = (numericHash << 5) - numericHash + char;
      numericHash = numericHash & numericHash; // Convert to 32bit integer
    }

    return hash + "_" + Math.abs(numericHash).toString(36);
  }

  // 重新加载主要内容
  async reloadMainContent() {
    try {
      console.log("🔄 重新加载主要内容...");
      const mainResponse = await fetch(`/pages/${this.currentPageId}/main.html?t=${Date.now()}`);

      if (!mainResponse.ok) {
        throw new Error(`HTTP ${mainResponse.status}: ${mainResponse.statusText}`);
      }

      let mainContent = await mainResponse.text();

      // 处理资源文件路径
      if (this.currentPageConfig && this.currentPageConfig.assetsConfig) {
        mainContent = this.processAssetUrls(mainContent, this.currentPageId, this.currentPageConfig.assetsConfig);
      }

      const mainPlaceholder = document.getElementById("main-placeholder");
      if (!mainPlaceholder) {
        throw new Error("找不到 main-placeholder 元素");
      }

      // 保存滚动位置
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

      // 检查内容是否包含script标签，如果包含则使用特殊处理
      if (mainContent.includes("<script")) {
        console.log("📜 检测到script标签，使用特殊处理方式");
        this.insertContentWithScriptsAndStyles(mainContent, "main-placeholder");
      } else {
        // 普通HTML内容，直接使用innerHTML
        mainPlaceholder.innerHTML = mainContent;
      }

      // 恢复滚动位置
      window.scrollTo(0, scrollTop);

      // 重新执行页面脚本以重新绑定事件监听器
      console.log("🔄 重新初始化页面脚本...");
      await this.reinitializePageScript();

      console.log("✅ 主要内容重新加载完成");
      this.showUpdateNotification();
    } catch (error) {
      console.error("❌ 重新加载主要内容失败:", error);
      this.showErrorNotification(error.message);
    }
  }

  // 重新加载样式
  async reloadStyles() {
    try {
      console.log("🔄 重新加载样式...");

      // 获取当前页面的样式链接元素
      const pageStyleLink = document.getElementById("page-style");
      if (pageStyleLink) {
        // 通过添加时间戳来强制刷新样式
        const baseHref = pageStyleLink.href.split("?")[0];
        pageStyleLink.href = `${baseHref}?t=${Date.now()}`;

        console.log("✅ 样式已刷新");
        this.showUpdateNotification("🎨 样式已更新");
      } else {
        console.log("📌 未找到页面样式链接，可能使用内联样式");
        this.showUpdateNotification("🎨 样式已更新");
      }
    } catch (error) {
      console.error("❌ 重新加载样式失败:", error);
      this.showErrorNotification(error.message);
    }
  }

  // 重新加载页面脚本
  async reloadPageScript() {
    try {
      console.log("🔄 重新加载页面脚本...");

      const scriptUrl = `/pages/${this.currentPageId}/script.js`;

      // 移除现有的页面脚本
      const existingScript = document.querySelector(`script[src^="${scriptUrl}"]`);
      if (existingScript) {
        existingScript.remove();
        console.log("🧹 已移除旧的页面脚本");
      }

      // 等待一小段时间确保脚本完全移除
      await new Promise((resolve) => setTimeout(resolve, 100));

      // 等待jQuery加载完成
      await this.waitForJQuery();

      // 加载新的页面脚本
      await new Promise((resolve, reject) => {
        const script = document.createElement("script");
        script.src = `${scriptUrl}?t=${Date.now()}`;
        script.type = "module";
        script.onload = () => {
          console.log("✅ 页面脚本重新加载成功");
          resolve();
        };
        script.onerror = () => {
          console.warn("⚠️ 页面脚本重新加载失败");
          reject(new Error(`Failed to reload page script: ${scriptUrl}`));
        };
        document.head.appendChild(script);
      });

      this.showUpdateNotification("📜 页面脚本已更新");
    } catch (error) {
      console.error("❌ 重新加载页面脚本失败:", error);
      this.showErrorNotification(error.message);
    }
  }

  // 显示更新提示
  showUpdateNotification(message = "页面内容已更新") {
    const notification = document.createElement("div");
    notification.style.cssText = `
      position: fixed;
      top: 60px;
      right: 20px;
      background: #27ae60;
      color: white;
      padding: 12px 20px;
      border-radius: 6px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.2);
      z-index: 10000;
      font-size: 14px;
      opacity: 0;
      transform: translateX(100%);
      transition: all 0.3s ease;
    `;
    notification.innerHTML = `✅ ${message}`;
    document.body.appendChild(notification);

    // 显示动画
    requestAnimationFrame(() => {
      notification.style.opacity = "1";
      notification.style.transform = "translateX(0)";
    });

    // 3秒后自动消失
    setTimeout(() => {
      notification.style.opacity = "0";
      notification.style.transform = "translateX(100%)";
      setTimeout(() => {
        if (document.body.contains(notification)) {
          document.body.removeChild(notification);
        }
      }, 300);
    }, 3000);
  }

  // 显示错误提示
  showErrorNotification(errorMessage) {
    const notification = document.createElement("div");
    notification.style.cssText = `
      position: fixed;
      top: 60px;
      right: 20px;
      background: #e74c3c;
      color: white;
      padding: 12px 20px;
      border-radius: 6px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.2);
      z-index: 10000;
      font-size: 14px;
      opacity: 0;
      transform: translateX(100%);
      transition: all 0.3s ease;
      max-width: 300px;
    `;
    notification.innerHTML = `❌ 更新失败: ${errorMessage}`;
    document.body.appendChild(notification);

    // 显示动画
    requestAnimationFrame(() => {
      notification.style.opacity = "1";
      notification.style.transform = "translateX(0)";
    });

    // 5秒后自动消失
    setTimeout(() => {
      notification.style.opacity = "0";
      notification.style.transform = "translateX(100%)";
      setTimeout(() => {
        if (document.body.contains(notification)) {
          document.body.removeChild(notification);
        }
      }, 300);
    }, 5000);
  }
}

// 全局实例
window.previewGenerator = new PreviewGenerator();

// 开发工具函数
window.editMain = () => {
  const pageId = window.previewGenerator.currentPageId;
  alert(`请在编辑器中打开：\nsrc/pages/${pageId}/main.html`);
};

window.editStyle = () => {
  const pageId = window.previewGenerator.currentPageId;
  alert(`请在编辑器中打开：\nsrc/pages/${pageId}/style.scss`);
};

window.editScript = () => {
  const pageId = window.previewGenerator.currentPageId;
  alert(`请在编辑器中打开：\nsrc/pages/${pageId}/script.js`);
};

window.buildPage = () => {
  alert("请在终端运行：\nnpm run build");
};

// 自动初始化
document.addEventListener("DOMContentLoaded", () => {
  window.previewGenerator.init();
});
