# 🎬 视频资源目录

这个目录用于存放项目中使用的各类视频资源文件，支持自动路径转换和独立 CDN 配置。

## 📋 支持的视频格式

本项目支持以下视频格式的自动路径处理：

- `.mp4` - 最广泛支持的视频格式，推荐首选
- `.webm` - 开源格式，体积小，适合网页视频
- `.ogg` - 开源视频容器格式
- `.avi` - 传统视频格式
- `.mov` - 苹果 QuickTime 视频格式

## 🔧 使用方法

### HTML 中引用视频

在`main.html`中使用相对路径引用视频文件：

```html
<!-- 基本视频嵌入 -->
<video src="assets/videos/demo.mp4" controls></video>

<!-- 带预览图的视频 -->
<video src="assets/videos/tutorial.mp4" poster="assets/images/video-preview.jpg" controls></video>

<!-- 响应式视频 -->
<div class="video-container">
  <video src="assets/videos/responsive-video.mp4" controls preload="metadata"></video>
</div>

<!-- 带多个源的视频（不同格式） -->
<video controls width="640" height="360">
  <source src="assets/videos/multi-format.mp4" type="video/mp4" />
  <source src="assets/videos/multi-format.webm" type="video/webm" />
  您的浏览器不支持HTML5视频。
</video>
```

### 独立的视频 CDN 配置

项目支持为视频文件配置独立的 CDN 路径，与图片和其他资源分离：

```json
// pages.json 配置示例
{
  "id": "your-page",
  "assetsConfig": {
    "baseUrl": "https://images.example.com/assets/your-page/",
    "videoBaseUrl": "https://videos.example.com/assets/your-page/",
    "videoExtensions": [".mp4", ".webm", ".ogg", ".avi", ".mov"]
  }
}
```

构建过程将自动识别视频文件并应用正确的 CDN 基础路径。

## 📊 视频性能优化

### 推荐设置

- **preload="metadata"**: 仅预加载视频元数据，不加载全部内容
- **loading="lazy"**: 懒加载视频，提高页面加载速度
- **playsinline**: 在移动设备上内联播放
- 使用合适的尺寸和压缩设置
- 为重要内容提供多种格式（mp4 + webm）

### SCSS 样式示例

```scss
// 响应式视频容器
.video-container {
  position: relative;
  padding-bottom: 56.25%; // 16:9 宽高比
  height: 0;
  overflow: hidden;

  video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}
```

## 📝 最佳实践

- 尽可能使用 MP4 格式，确保最广泛的兼容性
- 为所有视频添加预览图（poster 属性）
- 优化视频文件大小，避免超过 10MB 的大文件
- 考虑为重要视频提供多种分辨率和格式
- 使用视频压缩工具减小文件大小
- 仅在必要时自动播放视频，避免影响用户体验和性能
- 对于大型视频，考虑使用外部视频托管服务（如 YouTube、Vimeo）
