* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

main {
  background-color: #f5f8ff;
  color: #000;
  font-family: "Mulish", -apple-system, blinkmacsystemfont, "Segoe UI", roboto, "Helvetica Neue", arial, "Noto Sans", sans-serif, "Apple Color Emoji",
    "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";

  @media (max-width: 1280px) {
    background-color: #f4f7ff;
  }
  video {
    height: 100%;
    width: 100%;
    object-fit: cover;
    line-height: 0;
    font-size: 0;
    // google去黑线
    filter: grayscale(0);
    // 火狐去黑线
    clip-path: fill-box;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  p,
  div,
  span,
  ul,
  li {
    margin-bottom: 0;
    font-family: "Mulish", -apple-system, blinkmacsystemfont, "Segoe UI", roboto, "Helvetica Neue", arial, "Noto Sans", sans-serif, "Apple Color Emoji",
      "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  }

  h2 {
    text-align: center;
    font-weight: 800;
    font-size: 2.25rem;
  }

  h1,
  h2,
  h3 {
    text-align: center;
  }

  h2 {
    font-size: 2.25rem;
    font-weight: 800;
  }

  .opacity-7 {
    opacity: 0.7;
  }

  .blue-text {
    color: #0055fb;
  }

  .blue-link {
    font-weight: 700;
    color: #0055fb;
    &:hover {
      color: #0055fb;
    }
  }

  .gray-text {
    color: #454545;
  }

  .btn-wrapper {
    display: flex;
    justify-content: center;
    gap: 1rem;
  }

  @media (max-width: 768px) {
    .btn-wrapper {
      flex-direction: column;
      gap: 8px;
    }
  }

  .btn-wrapper .btn {
    margin: 0;
    border-radius: 12px;
    text-transform: capitalize;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 158px;
    gap: 0.5rem;
    &.btn-lg {
      min-width: 228px;
    }
  }

  @media (max-width: 768px) {
    .btn-wrapper .btn {
      display: block;
      vertical-align: baseline;
    }
  }

  .btn-download {
    background: linear-gradient(85.49deg, #0458ff 0%, #0499ff 100%);
    border: none;
    color: #fff;
    background-color: #0458ff;
  }

  .btn-download:hover {
    color: #fff;
    background: linear-gradient(0deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.2)), linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%),
      linear-gradient(0deg, #0055fb, #0055fb);
    background-color: #0458ff;
  }

  .swiper-pagination {
    bottom: -4px !important;
  }

  .swiper-pagination .swiper-pagination-bullet {
    width: 8px;
    height: 8px;
    background-color: #c2cee9;
    opacity: 1;
  }

  .swiper-pagination .swiper-pagination-bullet-active {
    width: 64px;
    background: linear-gradient(89.5deg, #0458ff 0%, #0499ff 100%);
    border-radius: 8px;
  }
  .part-banner {
    text-align: center;
    background: url(assets/images/banner-bg.jpg) no-repeat center center / cover;
    position: relative;
    z-index: 1;
    .sub-title {
      display: flex;
      gap: 8px;
      align-items: center;
      justify-content: center;
      .blue-tip {
        background: linear-gradient(100.2deg, #0055fb 0%, #00c1ff 100%), linear-gradient(0deg, #d9d9d9, #d9d9d9);
        border-radius: 24px;
        padding: 4px 12px;
        font-weight: 700;
        font-size: 1.25rem;
        line-height: 100%;
        color: #fff;
      }
    }
    h1 {
      background: linear-gradient(95.44deg, #0055fb -28.33%, #00c1ff 96.36%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-fill-color: transparent;
    }
    .feature-list {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 4px 8px;
      border-radius: 1.5rem;
      border: 1.5px solid #0055fb;
      min-width: 602px;
      flex-wrap: wrap;
      @media (max-width: 768px) {
        min-width: unset;
      }
      .feature-item {
        flex: 1;
        font-weight: 600;
        font-size: 1.125rem;
        color: #0055fb;
        padding: 0 1rem;
        position: relative;
        &.text-gradient {
          background: linear-gradient(92.01deg, #e046ff 4.9%, #7970ff 52.55%, #0066ff 99.48%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          text-fill-color: transparent;
        }
        @media (max-width: 768px) {
          font-size: 14px;
          padding: 0 6px;
        }
        &:not(:last-child)::after {
          content: "";
          display: inline-block;
          width: 1px;
          height: 80%;
          background-color: #0055fb;
          top: 50%;
          transform: translateY(-50%);
          position: absolute;
          right: 0;
        }
      }
    }
    .img-wrapper {
      position: relative;
      .banner-white-wrapper {
        position: absolute;
        left: 50%;
        bottom: 10%;
        transform: translateX(-50%);
        z-index: 1;
        width: 34.61%;
        aspect-ratio: 996 / 127;
        background: url(assets/images/white-bg.png) no-repeat center center / contain;
        overflow: hidden;

        .banner-white-inner {
          position: relative;

          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          margin-right: 4%;
          margin-left: 2%;
          overflow: hidden;
          .banner-white-link {
            display: block;
            position: absolute;
            width: 100%;
            height: 100%;
            z-index: 5;
            top: 0;
            left: 0;
          }
          .banner-white-image {
            position: absolute;
            top: 10%;
            left: 0;

            height: 80%;
          }
          @keyframes marquee1 {
            0% {
              transform: translateX(-50%);
            }
            100% {
              transform: translateX(0);
            }
          }
          .banner-note-group {
            position: absolute;
            top: 20%;
            left: 0%;
            height: 60%;
            width: 100%;
            flex-wrap: nowrap;
            margin-left: 13%;
            overflow: hidden;

            .banner-note-group-inner {
              display: flex;

              height: 100%;
              display: flex;
              width: max-content;
              animation: marquee1 8s linear infinite;
            }
          }
        }
      }
    }

    #banner-text-swiper {
      height: 22px;
      overflow: hidden;
      @media (max-width: 576px) {
        height: 44px;
      }
    }
    .detail-item {
      display: flex;
      align-items: flex-start;
      justify-content: center;
      gap: 10px;
      font-size: 14px;
      @media (max-width: 576px) {
        text-align: left;
      }
    }
  }
  .part-logos {
    position: relative;
    z-index: 2;
    .logos-wrapper {
      background-color: #fff;
      border-radius: 1rem;
      padding: 2rem 0.5rem;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    @media (max-width: 768px) {
      .logos-wrapper {
        padding: 1.5rem 0.5rem;
      }
    }

    .logos-wrapper .logo-item {
      flex: 1 1 33%;
      max-height: 2rem;
      text-align: center;
    }

    .logos-wrapper .logo-item:not(:last-child) {
      border-right: 1px solid rgba(0, 0, 0, 0.1);
    }

    .logos-wrapper .logo-item img {
      max-width: 100%;
      max-height: 2rem;
      object-fit: contain;
    }

    @media (max-width: 768px) {
      .logos-wrapper .logo-item img {
        max-height: 1.1rem;
      }
    }
  }
  .part-audio {
    .audio-title {
      border-radius: 0.5rem;
      background: linear-gradient(86.47deg, rgba(148, 219, 255, 0.4) 1.47%, rgba(143, 209, 255, 0.4) 96.84%);
      text-align: center;
      font-size: 1.25rem;
      font-weight: 500;
      padding: 0.75rem;
      margin-bottom: 1rem;
    }
    .audio-box {
      border-radius: 0.5rem;
      background-color: #fff;
      padding: 1.5rem;
      &:not(:last-child) {
        margin-bottom: 0.75rem;
      }
      .audio-type {
        font-size: 1.5rem;
        font-weight: 800;
        color: #000;
        line-height: 100%;
        margin-bottom: 0.5rem;
      }
      .audio-demo {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 0.5rem;
        margin-top: 1.25rem;
        .audio-info {
          display: flex;
          align-items: center;

          .audio-label {
            font-size: 0.875rem;
            color: #555555;
            font-weight: 500;
          }
        }
        .audio-player {
          flex: 1;
          display: flex;
          align-items: center;
          gap: 5px;
          border-radius: 0.5rem;
          background-color: #edf9ff;
          padding: 4px 0.875rem;
          max-width: 71%;
          @media (max-width: 576px) {
            max-width: 56%;
          }
          .play-button {
            display: flex;
            align-items: center;
            width: 1rem;
            cursor: pointer;
            .play-icon {
              display: inline-block;
              height: 100%;
              width: 100%;
            }
            .pause-icon {
              display: none;
            }
          }
          .progress-container {
            display: flex;
            align-items: center;
            gap: 8px;
            flex: 1;

            .progress-bar {
              flex: 1;
              height: 4px;
              background: #ffffff;
              border-radius: 2px;
              position: relative;
              .progress-fill {
                height: 100%;
                background: #1a8dff;
                border-radius: 2px;
                width: 0%;
                transition: width 0.2s linear;
              }
            }
            .time-display {
              font-size: 0.875rem;
              font-weight: 500;
              color: #6e869f;
            }
          }
        }
      }
    }
  }

  .part-expertly {
    background: linear-gradient(171.15deg, #afdaff -17.26%, #d1e9fd 31.35%, rgba(245, 248, 255, 0) 93.26%), linear-gradient(0deg, #f5f8ff, #f5f8ff);
    .audio-section {
      display: flex;
      justify-content: space-between;
      gap: 1.875rem;
      @media (max-width: 1280px) {
        flex-direction: column;
      }
      .audio-accordion-wrapper {
        width: calc(41.42% - 0.9375rem);
        @media (max-width: 1280px) {
          width: 100%;
        }
        .audio-accordion {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          gap: 0.5rem;
          height: 100%;
          .audio-accordion-item {
            border-radius: 1rem;
            overflow: hidden;
            background: rgba(255, 255, 255, 0.53);
            padding: 1.25rem 1rem;
            position: relative;
            @media (max-width: 1280px) {
              padding: 0.5rem;
            }
            &:has([aria-expanded="true"]) {
              background-color: #ffffff;
              box-shadow: 0px 2px 20px 0px #00000012;

              .audio-progress {
                display: block;
              }
            }

            .audio-accordion-header {
              display: flex;
              align-items: center;
              gap: 4px;
              cursor: pointer;

              .audio-title {
                font-weight: 700;
                font-size: 1.25rem;
              }
            }
            .audio-content {
              color: #787878;
              padding-left: 2.7rem;
              padding-bottom: 0.5rem;
              font-size: 1rem;
              line-height: 1.5rem;
            }
            .audio-progress {
              position: absolute;
              bottom: 0;
              left: 0;
              width: 100%;
              height: 5px;
              display: none;

              background-color: #e8eaec;
              .audio-progress-bar {
                height: 100%;
                background: linear-gradient(95.44deg, #0055fb -28.33%, #00c1ff 96.36%);
                width: 0%;
              }
            }
          }
        }
      }
      .audio-media {
        width: calc(58.58% - 0.9375rem);
        @media (max-width: 1280px) {
          width: 100%;
        }
        .img-wrapper {
          border-radius: 1rem;
          overflow: hidden;
          position: relative;
          .audio-white-wrapper {
            position: absolute;
            left: 50%;
            bottom: 4.46%;
            transform: translateX(-50%);
            z-index: 1;
            width: 84.94%;
            aspect-ratio: 996 / 127;
            background: url(assets/images/white-bg.png) no-repeat center center / contain;
            overflow: hidden;
            @keyframes audio-rotate {
              0% {
                transform: rotate(360deg);
              }
              100% {
                transform: rotate(0deg);
              }
            }

            .audio-white-link {
              display: block;
              position: absolute;
              width: 100%;
              height: 100%;
              z-index: 5;
              top: 0;
              left: 0;
            }
            .audio-white-image {
              position: absolute;
              top: 11.5%;
              left: 2%;
              height: 77%;
              z-index: 4;
              animation: audio-rotate 3s linear infinite;
            }
            .audio-note-image {
              position: absolute;
              top: 22%;
              left: 14%;
              height: 56%;
              z-index: 3;
            }
          }
        }
      }
    }
  }

  .part-corruption {
    background-color: #f5f8ff;
    .nav {
      display: flex;
      max-width: 930px;
      margin: 0 auto;
      border-radius: 99px;
      padding: 4px;
      overflow: hidden;
      background-color: #9dd7ff;
      .nav-item {
        flex: 1;
        text-align: center;
        padding: 0.5rem;
        cursor: pointer;
        font-weight: 800;
        font-size: 1.25rem;
        color: #fff;
        overflow: hidden;
        border-radius: 99px;
        &.active {
          background-color: #fff;
          color: #0458ff;
        }
      }
    }
    .symptom-list {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-template-rows: repeat(2, 1fr);
      gap: 1.25rem;
      grid-template-areas:
        "symptom-item-1 symptom-item-2 symptom-item-3"
        "symptom-item-1 symptom-item-4 symptom-item-5";
      @media (max-width: 992px) {
        gap: 0.5rem;
      }
      @media (max-width: 768px) {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
      }
      .symptom-item {
        position: relative;
        overflow: hidden;

        .symptom-label {
          position: absolute;
          left: 50%;
          bottom: 0.5rem;
          font-weight: 800;
          font-size: 1.5rem;
          line-height: 2.75rem;
          transform: translateX(-50%);
          color: #000;
          z-index: 3;
          text-align: center;
          width: 100%;
          @media (max-width: 768px) {
            display: none;
          }
        }
        .symptom-detail {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          z-index: 5;
          transform: translateY(101%);
          transition: all 0.3s cubic-bezier(0.05, 0.61, 0.41, 0.95);
          background-color: #fff;
          padding: 1.25rem 2rem;
          border-radius: 1.5rem;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
          text-align: center;
          box-shadow: 0px 0px 12px 0px #00d1ff4d;
          background: linear-gradient(193.86deg, #dcf9ff 9.89%, #ffffff 90.02%);

          &::after {
            content: "";
            position: absolute;
            inset: 0;
            padding: 1px;
            border-radius: 1rem;
            background: linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%);
            mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
            mask-composite: exclude;
            pointer-events: none;
          }
          @media (max-width: 768px) {
            transform: translateY(0);
            bottom: 0;
            top: unset;
            height: auto;
            background: unset;
            box-shadow: none;
            padding: 1rem;
            padding-top: 3rem;
            background: linear-gradient(180deg, rgba(214, 238, 255, 0) 5.68%, #b1dfff 100%);

            &::after {
              display: none;
            }
          }

          .symptom-detail-title {
            font-weight: 900;
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            @media (max-width: 1280px) {
              font-size: 1.25rem;
            }
          }
          .symptom-detail-desc {
            font-size: 1.25rem;
            @media (max-width: 1280px) {
              font-size: 1rem;
            }
          }
        }
        @media (any-hover: hover) {
          &:hover {
            .symptom-detail {
              transform: translateY(0);
            }
          }
        }
      }
    }
    .fileerror-list {
      display: grid;
      grid-template-columns: repeat(3, 1fr);

      gap: 1.25rem;
      grid-template-areas:
        "fileerror-item-1 fileerror-item-2 fileerror-item-2"
        "fileerror-item-1 fileerror-item-3 fileerror-item-4";
      @media (max-width: 992px) {
        gap: 0.5rem;
      }
      @media (max-width: 768px) {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
      }
      .fileerror-item {
        position: relative;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        &.no-sound-on-playback {
          .fileerror-content {
            max-width: 456px;
            width: 60%;
            @media (max-width: 768px) {
              width: 100%;
              max-width: unset;
            }
          }
          @media (max-width: 576px) {
            img {
              min-height: 200px;
            }
          }
        }
        .fileerror-content {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          padding: 2rem;

          display: flex;
          flex-direction: column;

          @media (max-width: 1280px) {
            padding: 1rem;
          }

          .fileerror-label {
            font-size: 1.5rem;
            font-weight: 800;
            color: #000;
            margin-bottom: 4px;
            @media (max-width: 1280px) {
              font-size: 1.25rem;
            }
          }
          .fileerror-desc {
            font-size: 1rem;
            color: #444444;
            margin-bottom: 0.625rem;
          }
          .fileerror-link {
            font-size: 1.25rem;
            color: #1a8dff;
          }
        }
      }
    }
    @media (min-width: 1280px) {
      #card-swiper .swiper-wrapper {
        gap: 1.875rem;
        flex-wrap: nowrap;
      }

      #card-swiper .swiper-wrapper .swiper-slide {
        flex: 1 1 calc(20% - 1.875rem);
      }
    }
    .card-box {
      padding: 2rem 1.5rem;
      border-radius: 1.5rem;
      background-color: #fff;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      height: 100%;
      .card-icon {
        width: 6rem;
      }
      .card-title {
        font-size: 1.125rem;
        font-weight: 800;
        color: #000;
        margin-bottom: 0;
      }
      .card-desc {
        font-size: 14px;
        color: #000;
        opacity: 0.6;
        text-align: center;
      }
    }
  }

  .part-steps {
    background: radial-gradient(82.32% 135.56% at 31.17% -26.53%, #b6e0ff 0%, #e3f3ff 50%, #e0f2ff 100%);

    .nav-item {
      padding: 1.5rem;
      border-radius: 12px;
      width: 100%;
    }

    .nav-item.active {
      background-color: #fff;
    }

    .nav-item .nav-item-content {
      display: flex;
      align-items: flex-start;
    }
  }
}
main .part-faq .accordion-box {
  background-color: #fff;
  border-radius: 1.5rem;
  padding: 0.5rem 4rem;
}

@media (max-width: 992px) {
  main .part-faq .accordion-box {
    padding: 0.5rem 2rem;
  }
}

@media (max-width: 768px) {
  main .part-faq .accordion-box {
    padding: 0.5rem 1rem;
  }
}

main .part-faq .accordion-box .accordion-item {
  padding: 1.5rem;
}

main .part-faq .accordion-box .accordion-item:not(:last-child) {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

main .part-faq .accordion-box .accordion-item svg {
  transition: all 0.2s linear;
}

@media (max-width: 768px) {
  main .part-faq .accordion-box .accordion-item svg {
    width: 1rem;
  }
}

@media (max-width: 768px) {
  main .part-faq .accordion-box .accordion-item {
    padding: 1rem 0.5rem;
  }
}

main .part-faq .accordion-box .accordion-item [aria-expanded="true"] svg {
  transform: rotate(180deg);
}

main .part-faq .accordion-box .accordion-item .serial-number {
  display: inline-flex;
  width: 22px;
  height: 22px;
  align-items: center;
  justify-content: center;
  color: #fff;
  background: linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%);
  border-radius: 50%;
  margin-right: 8px;
  font-size: 1rem;
  font-weight: 800;
  flex-shrink: 0;
}

@media (max-width: 768px) {
  main .part-faq .accordion-box .accordion-item .serial-number {
    width: 16px;
    height: 16px;
    color: #fff;
  }
}

main .part-faq .accordion-box .accordion-item .faq-detail {
  font-size: 14px;
  padding-top: 1rem;
  opacity: 0.7;
  padding-left: 30px;
  padding-right: 32px;
}

@media (max-width: 768px) {
  main .part-faq .accordion-box .accordion-item .faq-detail {
    padding-left: 20px;
    padding-right: 16px;
  }
}

main .part-stories .swiper {
  margin: 2rem;
}

@media (max-width: 768px) {
  main .part-stories .swiper {
    margin: 0.5rem;
  }
}

@media (min-width: 768px) {
  main .part-stories .swiper-slide .user-wrapper::before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    background-color: rgba(210, 223, 255, 0.3);
    z-index: 2;
  }

  main .part-stories .swiper-slide.swiper-slide-active .user-wrapper::before {
    content: unset;
  }
}

main .part-stories .user-wrapper {
  border-radius: 1rem;
  overflow: hidden;
  position: relative;
}

@media (max-width: 768px) {
  main .part-stories .user-wrapper::before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    background-color: rgba(0, 0, 0, 0.6);
  }
}

main .part-stories .user-wrapper .user-story {
  position: absolute;
  right: 4rem;
  top: 3rem;
  max-width: 360px;
  color: #fff;
}

@media (max-width: 768px) {
  main .part-stories .user-wrapper .user-story {
    right: 0;
    top: 0;
    width: 100%;
    height: 100%;
    padding: 8px;
    color: #fff;
  }
}

main .part-stories .user-wrapper .user-story .user-occupation {
  font-size: 14px;
  margin-bottom: 16px;
}

main .part-stories .user-wrapper .user-story .user-comments {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

@media (max-width: 768px) {
  main .part-stories .user-wrapper .user-story .user-comments {
    color: #fff;
  }
}

main .part-stories .swiper-pagination {
  bottom: -2.5rem !important;
}

main .part-links .part-links-line {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

main .part-links .line-border {
  border-right: 1px solid rgba(0, 0, 0, 0.3);
  border-left: 1px solid rgba(0, 0, 0, 0.3);
}

@media (max-width: 1280px) {
  main .part-links .line-border {
    border-right: unset;
  }
}

@media (max-width: 768px) {
  main .part-links .line-border {
    border-left: unset;
  }
}

main .part-links .text-link {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.7);
  margin-top: 1.5rem;
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

main .part-links .text-link:hover {
  color: #0055fb;
}

main .part-links .part-links-videos {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

main .part-links .part-links-videos .video-wrapper {
  border-radius: 0.75rem;
}

@media (max-width: 1280px) {
  main .part-links .part-links-videos {
    flex-direction: row;
    padding-top: 2rem;
  }
}

@media (max-width: 576px) {
  main .part-links .part-links-videos {
    display: block;
  }
}

main .part-links .text-line4 {
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

main .part-footer {
  background-image: url(https://images.wondershare.com/repairit/images2024/index/part-footer-white.jpg);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  @media (max-width: 576px) {
    .display-2 {
      font-size: 2.5rem;
    }
  }
}

main .part-footer-logo {
  height: 4rem;
  width: 14.5rem;
  margin: 0 auto;
}

@media (max-width: 576px) {
  main .part-footer .btn-outline-action {
    background-color: #fff;
    vertical-align: text-bottom;
  }
}

main .part-advanced .advanced-item {
  border-radius: 1rem;
  background-color: #ffffff;
  overflow: hidden;
  height: 100%;
}

main .part-advanced .advanced-item .compare-before {
  position: absolute;
  width: 50%;
  height: 100%;
  left: 0;
  top: 0;
  background-size: auto 100%;
  background-repeat: no-repeat;
  z-index: 2;
}

main .part-advanced .advanced-item .compare-before::after {
  content: "";
  width: 2px;
  height: 100%;
  background: #fff;
  position: absolute;
  right: 0;
  top: 0;
}

main .part-advanced .advanced-item .compare-before::before {
  content: "";
  background-image: url(https://images.wondershare.com/repairit/images2024/index/slide-tip.svg);
  background-size: contain;
  background-position: center;
  width: 4.25rem;
  height: 2.5rem;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translate(50%, -50%);
  z-index: 3;
}

@keyframes changeWidth {
  0% {
    width: 0;
  }

  50% {
    width: 100%;
  }

  100% {
    width: 0;
  }
}

main .part-advanced .advanced-item .compare-before.compare-before-1 {
  background-image: url(assets/images/video-repair-before.jpg);
  animation: changeWidth 8s linear infinite;
}

main .part-advanced .advanced-item .compare-before.compare-before-2 {
  background-image: url(assets/images/photo-repair-before.jpg);
  animation: changeWidth 7s linear infinite;
}

main .part-advanced .advanced-item .compare-before.compare-before-3 {
  background-image: url(assets/images/file-repair-before.jpg);
  animation: changeWidth 7s linear infinite 1s;
}

main .part-advanced .advanced-item .compare-before.compare-before-4 {
  background-image: url(assets/images/ai-video-enhancer-before.jpg);
  animation: changeWidth 6s linear infinite;
}

main .part-advanced .advanced-item .slider {
  -webkit-appearance: none;
  appearance: none;
  outline: 0;
  margin: 0;
  background: 0 0;
  z-index: 3;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

main .part-advanced .advanced-item .slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 2px;
  height: auto;
  background: transparent;
}

main .part-advanced .advanced-item .item-link {
  color: #000;
}

main .part-advanced .advanced-item .item-link .normal-arrow {
  display: inline;
}

main .part-advanced .advanced-item .item-link .active-arrow {
  display: none;
}

main .part-advanced .advanced-item .item-link .arrow-icon {
  width: 2rem;
  display: inline-block;
}

@media (max-width: 576px) {
  main .part-advanced .advanced-item .item-link .arrow-icon {
    display: block;
  }
}

main .part-advanced .advanced-item .item-link:hover {
  color: #0458ff;
}

main .part-advanced .advanced-item .item-link:hover .normal-arrow {
  display: none;
}

main .part-advanced .advanced-item .item-link:hover .active-arrow {
  display: inline;
}
