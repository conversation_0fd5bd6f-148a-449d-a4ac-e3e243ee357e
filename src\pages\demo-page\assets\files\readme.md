# 📄 文档和文件资源目录

这个目录用于存放项目中使用的各类文档、压缩包和其他可下载文件资源。

## 📋 支持的文件格式

本项目支持以下文件格式的自动路径处理：

- **📑 文档类**

  - `.pdf` - PDF 文档
  - `.doc`, `.docx` - Word 文档
  - `.xls`, `.xlsx` - Excel 表格
  - `.ppt`, `.pptx` - PowerPoint 演示文稿
  - `.txt` - 文本文件

- **📦 压缩包**

  - `.zip` - ZIP 压缩包
  - `.rar` - RAR 压缩包
  - `.7z` - 7z 压缩包

- **💾 其他可下载文件**
  - `.csv` - CSV 数据文件
  - `.json` - JSON 数据文件
  - `.xml` - XML 数据文件

## 🔧 使用方法

### HTML 中引用

在`main.html`中使用相对路径引用文件：

```html
<!-- 下载链接 -->
<a href="assets/files/document.pdf" download>下载PDF文档</a>

<!-- 在线预览 -->
<a href="assets/files/presentation.pptx" target="_blank">查看演示文稿</a>

<!-- 带图标的下载链接 -->
<a href="assets/files/data.zip" download class="download-link"> <i class="icon-download"></i>下载数据包 </a>
```

### 构建过程

构建时会自动将相对路径转换为正确的生产环境路径：

```html
<!-- 开发环境相对路径 -->
<a href="assets/files/document.pdf" download>下载PDF文档</a>

<!-- 构建后自动转换为 -->
<a href="https://example.com/assets/your-page/files/document.pdf" download>下载PDF文档</a>
```

## 📝 最佳实践

- 保持文件名简洁且有描述性，尽量避免空格和特殊字符
- 对于大型文件，建议添加文件大小提示：`下载手册 (2.5MB)`
- 在适当情况下使用`download`属性确保文件下载而非在浏览器中打开
- 为文档添加适当的图标，提升用户体验
- 定期检查文件链接有效性

## 🔍 资源管理提示

- 请确保所有文件均有合法的使用权限
- 定期清理未使用的文件以减少项目体积
- 考虑为大型文件提供压缩版本
- 为中文名称的文件提供英文别名，避免 URL 编码问题
