// // 响应式混合器
// @mixin respond-to($breakpoint) {
//   @if map-has-key($breakpoints, $breakpoint) {
//     @media (max-width: map-get($breakpoints, $breakpoint)) {
//       @content;
//     }
//   } @else {
//     @warn "Unknown breakpoint: #{$breakpoint}. Available breakpoints: #{map-keys($breakpoints)}";
//   }
// }

// // 容器混合器
// @mixin container {
//   max-width: $container-max-width;
//   margin: 0 auto;
//   padding: 0 $spacing-md;

//   @include respond-to("mobile") {
//     padding: 0 $spacing-sm;
//   }
// }

// // 按钮混合器
// @mixin btn-base {
//   display: inline-flex;
//   align-items: center;
//   justify-content: center;
//   padding: $spacing-sm $spacing-lg;
//   border-radius: 50px;
//   border: none;
//   cursor: pointer;
//   font-size: $font-size-base;
//   font-weight: 600;
//   text-decoration: none;
//   transition: $transition-base;

//   &:hover {
//     transform: translateY(-2px);
//   }

//   &:active {
//     transform: translateY(0);
//   }
// }

// @mixin btn-primary {
//   @include btn-base;
//   background: $primary-color;
//   color: white;

//   &:hover {
//     background: darken($primary-color, 10%);
//   }
// }

// @mixin btn-secondary {
//   @include btn-base;
//   background: transparent;
//   color: $primary-color;
//   border: 2px solid $primary-color;

//   &:hover {
//     background: $primary-color;
//     color: white;
//   }
// }

// // 卡片混合器
// @mixin card {
//   background: white;
//   border-radius: $large-border-radius;
//   box-shadow: $box-shadow;
//   padding: $spacing-xl;
//   transition: $transition-base;

//   &:hover {
//     transform: translateY(-5px);
//     box-shadow: $large-box-shadow;
//   }
// }

// // 文本截断混合器
// @mixin text-truncate {
//   overflow: hidden;
//   text-overflow: ellipsis;
//   white-space: nowrap;
// }

// // 多行文本截断混合器
// @mixin text-truncate-multiline($lines: 2) {
//   display: -webkit-box;
//   -webkit-line-clamp: $lines;
//   -webkit-box-orient: vertical;
//   overflow: hidden;
// }

// // 清除浮动混合器
// @mixin clearfix {
//   &::after {
//     content: "";
//     display: table;
//     clear: both;
//   }
// }

// // 绝对居中混合器
// @mixin absolute-center {
//   position: absolute;
//   top: 50%;
//   left: 50%;
//   transform: translate(-50%, -50%);
// }

// // Flexbox居中混合器
// @mixin flex-center {
//   display: flex;
//   align-items: center;
//   justify-content: center;
// }

// // 渐变背景混合器
// @mixin gradient-bg($start-color: $primary-color, $end-color: $secondary-color) {
//   background: linear-gradient(135deg, $start-color 0%, $end-color 100%);
// }

// // 阴影混合器
// @mixin shadow($level: 1) {
//   @if $level == 1 {
//     box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
//   } @else if $level == 2 {
//     box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
//   } @else if $level == 3 {
//     box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
//   } @else if $level == 4 {
//     box-shadow: 0 16px 32px rgba(0, 0, 0, 0.25);
//   }
// }
