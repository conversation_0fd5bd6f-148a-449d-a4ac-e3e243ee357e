<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>可复用前端组件库 | 少女心代码展</title>

    <!-- Google Fonts: Quicksand for a soft, rounded look -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;700&display=swap" rel="stylesheet" />
    <!-- 可爱风格Favicon，小猫爪图标（更大尺寸） -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg width='128' height='128' viewBox='0 0 128 128' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cellipse cx='64' cy='80' rx='36' ry='28' fill='%23ffb6d5'/%3E%3Cellipse cx='36' cy='56' rx='8' ry='12' fill='%23fff'/%3E%3Cellipse cx='64' cy='48' rx='10' ry='14' fill='%23fff'/%3E%3Cellipse cx='92' cy='56' rx='8' ry='12' fill='%23fff'/%3E%3Cellipse cx='48' cy='88' rx='6' ry='8' fill='%23fff'/%3E%3Cellipse cx='80' cy='88' rx='6' ry='8' fill='%23fff'/%3E%3C/svg%3E">

    <!-- Prism.js for Syntax Highlighting (CSS) -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet" />

    <!-- Bootstrap CSS for accordion functionality -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css" rel="stylesheet" />

    <!-- Swiper CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@7/swiper-bundle.min.css" />

    <style>
      /* --- 1. 全局与主题样式 --- */
      :root {
        --bg-primary: #fff5f8; /* 淡粉色背景 */
        --bg-sidebar: #f2e7ff; /* 淡紫色侧边栏 */
        --text-primary: #5d546c; /* 深紫色文字，更柔和 */
        --accent-pink: #ff85a1; /* 活泼的粉色 */
        --accent-purple: #a486d8; /* 柔和的紫色 */
        --shadow-color: rgba(164, 134, 216, 0.15); /* 阴影颜色 */
        --border-color: #e8d9ff; /* 边框颜色 */
        --font-main: "Quicksand", "Helvetica Neue", sans-serif;
        --sidebar-width: 320px;
      }

      * {
        box-sizing: border-box;
        scrollbar-width: thin;
        scrollbar-color: var(--accent-pink) var(--bg-sidebar);
      }

      *::-webkit-scrollbar {
        width: 8px;
      }

      *::-webkit-scrollbar-track {
        background: var(--bg-sidebar);
      }

      *::-webkit-scrollbar-thumb {
        background-color: var(--accent-pink);
        border-radius: 10px;
        border: 2px solid var(--bg-sidebar);
      }

      html {
        scroll-behavior: smooth;
      }

      body {
        font-family: var(--font-main);
        background-color: var(--bg-primary);
        color: var(--text-primary);
        margin: 0;
        line-height: 1.7;
      }

      /* --- 2. 侧边栏导航 --- */
      .sidebar {
        position: fixed;
        top: 0;
        left: 0;
        width: var(--sidebar-width);
        height: 100vh;
        background-color: var(--bg-sidebar);
        padding: 30px 15px;
        overflow-y: auto;
        border-right: 2px solid var(--border-color);
        transition: transform 0.3s ease-in-out;
      }

      .sidebar-header {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--accent-purple);
        text-align: center;
        margin-bottom: 25px;
        padding-bottom: 20px;
        border-bottom: 1px dashed var(--accent-pink);
      }

      .sidebar-nav ul {
        list-style: none;
        padding: 0;
        margin: 0;
      }

      .sidebar-nav li a {
        display: block;
        text-decoration: none;
        color: var(--text-primary);
        padding: 10px 20px;
        margin-bottom: 8px;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
      }

      .sidebar-nav li a::before {
        content: "♡";
        position: absolute;
        left: -20px;
        color: var(--accent-pink);
        transition: left 0.3s ease;
      }

      .sidebar-nav li a:hover,
      .sidebar-nav li a.active {
        background-color: rgba(255, 255, 255, 0.6);
        color: var(--accent-pink);
        font-weight: 700;
        transform: translateX(5px);
        box-shadow: 0 4px 10px var(--shadow-color);
      }

      .sidebar-nav li a:hover::before,
      .sidebar-nav li a.active::before {
        left: 10px;
      }

      /* 导航子项目 */
      .sidebar-nav .sub-menu {
        padding-left: 20px;
        font-size: 0.9em;
      }
      .sidebar-nav .sub-menu li a {
        padding: 8px 15px;
        margin-bottom: 5px;
      }
      .sidebar-nav .sub-menu li a::before {
        content: "✧";
      }

      /* --- 3. 主内容区域 --- */
      .main-content {
        margin-left: var(--sidebar-width);
        padding: 40px 50px;
      }

      .component-section {
        background-color: #ffffff;
        border-radius: 20px;
        padding: 30px 40px;
        margin-bottom: 50px;
        box-shadow: 0 8px 25px var(--shadow-color);
        border: 1px solid white;
        transition: all 0.3s ease;
      }

      .component-section:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 35px rgba(164, 134, 216, 0.25);
        border-color: var(--accent-pink);
      }

      h1,
      h2,
      h3,
      h4 {
        font-weight: 700;
        color: var(--accent-purple);
      }

      h1 {
        font-size: 2.8rem;
        text-align: center;
        margin-bottom: 1rem;
        text-shadow: 2px 2px 5px var(--shadow-color);
      }

      h2 {
        font-size: 2rem;
        margin-top: 0;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 2px solid var(--border-color);
        color: var(--accent-pink);
      }

      h3 {
        /* For "目录", etc. */
        font-size: 1.5rem;
        color: var(--accent-purple);
        margin-top: 30px;
        margin-bottom: 15px;
      }

      h4 {
        /* For HTML, CSS, JS subheadings */
        font-size: 1.2rem;
        margin-top: 25px;
        margin-bottom: 10px;
        color: var(--text-primary);
        font-weight: 500;
        border-left: 4px solid var(--accent-pink);
        padding-left: 10px;
      }

      p {
        margin-bottom: 1em;
      }

      hr {
        border: 0;
        height: 1px;
        background-image: linear-gradient(to right, rgba(0, 0, 0, 0), var(--accent-pink), rgba(0, 0, 0, 0));
        margin: 40px 0;
      }

      a {
        color: var(--accent-pink);
        font-weight: 500;
      }

      a:hover {
        color: var(--accent-purple);
      }

      /* --- 4. 代码块样式 (Prism.js Override) --- */
      pre[class*="language-"] {
        border-radius: 12px;
        border: 1px solid var(--border-color);
        box-shadow: 0 5px 15px var(--shadow-color);
        margin: 1em 0;
        position: relative;
      }

      /* 自定义语言标签 */
      pre[class*="language-"]::before {
        position: absolute;
        top: -1px;
        right: 20px;
        padding: 3px 10px;
        font-family: var(--font-main);
        font-size: 0.8rem;
        font-weight: bold;
        color: white;
        background: var(--accent-pink);
        border-radius: 0 0 8px 8px;
        box-shadow: 0 2px 5px var(--shadow-color);
      }

      pre.language-html::before {
        content: "HTML";
        background: #f06529;
      }
      pre.language-css::before {
        content: "CSS";
        background: #2965f1;
      }
      pre.language-scss::before {
        content: "SCSS";
        background: #c6538c;
      }
      pre.language-javascript::before {
        content: "JavaScript";
        background: #f7df1e;
        color: #333;
      }

      /* --- 代码复制按钮样式 --- */
      .copy-btn {
        position: absolute;
        top: 8px;
        right: 8px;
        width: 32px;
        height: 32px;
        background: rgba(255, 255, 255, 0.9);
        border: 1px solid var(--border-color);
        border-radius: 6px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        z-index: 10;
        opacity: 0;
        transform: translateY(-5px);
      }

      pre[class*="language-"]:hover .copy-btn {
        opacity: 1;
        transform: translateY(0);
      }

      .copy-btn:hover {
        background: var(--accent-pink);
        border-color: var(--accent-pink);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(255, 133, 161, 0.3);
      }

      .copy-btn svg {
        width: 16px;
        height: 16px;
        fill: var(--text-primary);
        transition: fill 0.3s ease;
      }

      .copy-btn:hover svg {
        fill: white;
      }

      /* 复制成功状态 */
      .copy-btn.copied {
        background: #4caf50;
        border-color: #4caf50;
        transform: scale(1.1);
      }

      .copy-btn.copied svg {
        fill: white;
      }

      /* 复制成功提示 */
      .copy-tooltip {
        position: absolute;
        top: -40px;
        right: 0;
        background: #4caf50;
        color: white;
        padding: 6px 12px;
        border-radius: 6px;
        font-size: 0.8rem;
        font-weight: 500;
        opacity: 0;
        transform: translateY(10px);
        transition: all 0.3s ease;
        pointer-events: none;
        white-space: nowrap;
      }

      .copy-tooltip.show {
        opacity: 1;
        transform: translateY(0);
      }

      .copy-tooltip::after {
        content: "";
        position: absolute;
        top: 100%;
        right: 10px;
        border: 5px solid transparent;
        border-top-color: #4caf50;
      }

      /* --- 5. 组件概览链接样式 --- */
      .component-overview a {
        color: white;
        text-decoration: none;
        transition: all 0.3s ease;
        position: relative;
        display: inline-block;
      }

      .component-overview a:hover {
        color: #FFE066;
        transform: translateX(5px);
        text-shadow: 0 0 10px rgba(255, 224, 102, 0.5);
      }

      .component-overview a::before {
        content: "→";
        position: absolute;
        left: -15px;
        opacity: 0;
        transition: all 0.3s ease;
        color: #FFE066;
      }

      .component-overview a:hover::before {
        opacity: 1;
        left: -20px;
      }

      /* --- 6. 响应式设计 --- */
      @media (max-width: 1024px) {
        :root {
          --sidebar-width: 220px;
        }
        .main-content {
          padding: 30px;
        }
        h1 {
          font-size: 2.2rem;
        }
        h2 {
          font-size: 1.8rem;
        }
      }

      @media (max-width: 768px) {
        .sidebar {
          transform: translateX(-100%);
          z-index: 1000;
        }
        body.sidebar-open .sidebar {
          transform: translateX(0);
          box-shadow: 10px 0 30px rgba(0, 0, 0, 0.1);
        }
        .main-content {
          margin-left: 0;
          padding: 20px;
        }
        .component-section {
          padding: 20px;
        }

        .menu-toggle {
          display: block;
          position: fixed;
          top: 15px;
          left: 15px;
          z-index: 1001;
          width: 40px;
          height: 40px;
          background: var(--accent-pink);
          color: white;
          border: none;
          border-radius: 50%;
          font-size: 24px;
          line-height: 40px;
          text-align: center;
          box-shadow: 0 2px 10px var(--shadow-color);
        }
      }

      .menu-toggle {
        display: none;
      }

      /* --- Feature Accordion Swiper Component Styles --- */
      .feature-container {
        background-color: #1e1f22;
        border-radius: 1.5rem;
        padding: 2.5rem;
        display: flex;
        gap: 1.75rem;
        overflow: hidden;
      }

      @media (max-width: 992px) {
        .feature-container {
          flex-direction: column;
        }
      }

      @media (max-width: 576px) {
        .feature-container {
          padding: 1.5rem 1rem;
        }
      }

      .feature-text-wrapper {
        flex: 1;
        max-width: 482px;
        margin: 0 auto;
      }

      @media (max-width: 992px) {
        .feature-text-wrapper {
          max-width: 100%;
          width: 100%;
        }
      }

      .feature-title {
        font-weight: 600;
        font-size: 2rem;
        text-align: left;
        color: #fff;
        margin-bottom: 1.5rem;
      }

      @media (max-width: 992px) {
        .feature-title {
          text-align: center;
        }
      }

      .feature-accordion {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
      }

      .feature-accordion-item {
        border-radius: 0.5rem;
        overflow: hidden;
        padding: 0.75rem 1.5rem;
        position: relative;
        color: #fff;
      }

      .feature-accordion-item svg {
        transition: transform 0.3s ease-in-out;
      }

      .feature-accordion-item::after {
        content: "";
        position: absolute;
        inset: 0; 
        padding: 1px; 
        border-radius: 0.5rem;
        background: #393b3f;
        mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
        mask-composite: exclude;
        pointer-events: none;
      }

      /* Active State Styling */
      .feature-accordion-item:has([aria-expanded="true"]) {
        padding-top: 1.5625rem;
        padding-bottom: 1.5625rem;
        background: linear-gradient(86.47deg, rgba(4, 88, 255, 0.2) 1.47%, rgba(4, 153, 255, 0.2) 96.84%);
      }

      .feature-accordion-item:has([aria-expanded="true"])::after {
        background: linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%);
      }

      .feature-accordion-item:has([aria-expanded="true"]) svg {
        transform: rotate(-180deg);
      }

      .feature-accordion-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        line-height: 1;
        gap: 0.5rem;
        cursor: pointer;
      }

      .accordion-title {
        font-weight: 600;
      }

      .accordion-content {
        font-size: 14px;
        margin-top: 8px;
        color: #f1f1f1;
        padding-right: 1.5rem;
      }

      .feature-media-wrapper {
        width: 50%;
        flex-shrink: 0;
      }

      @media (max-width: 992px) {
        .feature-media-wrapper {
          width: 100%;
          order: -1;
          margin-bottom: 1.5rem;
        }
      }

      .feature-media-wrapper .swiper-slide {
        border-radius: 0.5rem;
        overflow: hidden;
      }

      .feature-media-wrapper .swiper-slide img,
      .feature-media-wrapper .swiper-slide video {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    </style>
  </head>
  <body>
    <button class="menu-toggle" id="menu-toggle">≡</button>

    <nav class="sidebar">
      <div class="sidebar-header">组件库 ♡</div>
      <ul class="sidebar-nav" id="sidebar-nav">
        <li><a href="#component-1">1. FAQ 手风琴</a></li>
        <li><a href="#component-2">2. 手风琴与 Swiper 联动组件</a></li>
        <li><a href="#component-3">3. PC卡片与Mobile轮播</a></li>
        <li><a href="#component-4">4. 页脚产品推广盒</a></li>
        <li><a href="#component-5">5. 无限滚动走马灯</a></li>
        <li><a href="#component-6">6. 纯 Tab 切换</a></li>
        <li><a href="#component-7">7. Tab与Swiper联动</a></li>
        <li><a href="#component-8">8. 悬浮水波纹下载按钮</a></li>
        <li><a href="#component-9">9. 高亮列对比表格</a></li>
        <li><a href="#component-10">10. 底部链接区域</a></li>
        <li>
          <a href="#component-11">11. 通用全局样式 (SCSS)</a>
          <ul class="sub-menu">
            <li><a href="#component-11-1">11.1 全局重置</a></li>
            <li><a href="#component-11-2">11.2 视频黑边修复</a></li>
            <li><a href="#component-11-3">11.3 自定义滚动条</a></li>
            <li><a href="#component-11-4">11.4 按钮样式</a></li>
            <li><a href="#component-11-5">11.5 Keyframe动画</a></li>
            <li><a href="#component-11-6">11.6 二维码悬停</a></li>
            <li><a href="#component-11-7">11.7 Swiper分页器</a></li>
          </ul>
        </li>
        <li>
          <a href="#component-12">12. 通用交互脚本 (JS)</a>
          <ul class="sub-menu">
            <li><a href="#component-12-1">12.1 AOS移动端禁用</a></li>
            <li><a href="#component-12-2">12.2 Swiper高度自适应</a></li>
            <li><a href="#component-12-3">12.3 Swiper纵向轮播</a></li>
            <li><a href="#component-12-4">12.4 Tab自动切换</a></li>
            <li><a href="#component-12-5">12.5 移动端专用Swiper</a></li>
            <li><a href="#component-12-6">12.6 Swiper淡入淡出</a></li>
            <li><a href="#component-12-7">12.7 视频懒加载</a></li>
            <li><a href="#component-12-8">12.8 GSAP滚动堆叠</a></li>
            <li><a href="#component-12-9">12.9 窗口变化重载</a></li>
            <li><a href="#component-12-10">12.10 表格行高同步</a></li>
            <li><a href="#component-12-11">12.11 辅助函数</a></li>
          </ul>
        </li>
        <li><a href="#component-13">13. 编辑器代码片段 (JSON)</a></li>
        <li><a href="#component-14">14. 通用卡片滑块/悬停组件</a></li>
      </ul>
    </nav>

    <main class="main-content">
      <h1>可复用前端组件库</h1>
      <p style="text-align: center; max-width: 600px; margin: 0 auto 40px auto">
        本文档整理了一系列可复用的前端组件，包括其 HTML 结构、CSS/SCSS 样式和相关的 JavaScript 交互逻辑。
      </p>

      <!-- 组件概览 -->
      <section class="component-section component-overview" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; margin-bottom: 50px;">
        <h2 style="color: white; border-bottom: 2px solid rgba(255,255,255,0.3);">📚 组件概览</h2>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-top: 30px;">
          <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px; backdrop-filter: blur(10px);">
            <h4 style="color: #FFE066; margin-bottom: 15px;">🎯 交互组件 (1-8)</h4>
            <ul style="list-style: none; padding: 0; margin: 0;">
              <li style="margin-bottom: 8px;"><a href="#component-1">• FAQ 手风琴</a></li>
              <li style="margin-bottom: 8px;"><a href="#component-2">• 手风琴与 Swiper 联动</a></li>
              <li style="margin-bottom: 8px;"><a href="#component-3">• PC卡片与Mobile轮播</a></li>
              <li style="margin-bottom: 8px;"><a href="#component-4">• 页脚产品推广盒</a></li>
              <li style="margin-bottom: 8px;"><a href="#component-5">• 无限滚动走马灯</a></li>
              <li style="margin-bottom: 8px;"><a href="#component-6">• 纯 Tab 切换</a></li>
              <li style="margin-bottom: 8px;"><a href="#component-7">• Tab与Swiper联动</a></li>
              <li style="margin-bottom: 8px;"><a href="#component-8">• 悬浮水波纹下载按钮</a></li>
            </ul>
          </div>
          <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px; backdrop-filter: blur(10px);">
            <h4 style="color: #66D9EF; margin-bottom: 15px;">🎨 布局组件 (9-10)</h4>
            <ul style="list-style: none; padding: 0; margin: 0;">
              <li style="margin-bottom: 8px;"><a href="#component-9">• 高亮列对比表格</a></li>
              <li style="margin-bottom: 8px;"><a href="#component-10">• 底部链接区域</a></li>
            </ul>
            <h4 style="color: #A6E22E; margin-bottom: 15px; margin-top: 20px;">⚙️ 工具组件 (11-14)</h4>
            <ul style="list-style: none; padding: 0; margin: 0;">
              <li style="margin-bottom: 8px;"><a href="#component-11">• 通用全局样式 (SCSS)</a></li>
              <li style="margin-bottom: 8px;"><a href="#component-12">• 通用交互脚本 (JS)</a></li>
              <li style="margin-bottom: 8px;"><a href="#component-13">• 编辑器代码片段 (JSON)</a></li>
              <li style="margin-bottom: 8px;"><a href="#component-14">• 通用卡片滑块/悬停组件</a></li>
            </ul>
          </div>
        </div>
        <div style="text-align: center; margin-top: 30px; padding: 20px; background: rgba(255,255,255,0.1); border-radius: 10px;">
          <h4 style="color: #F92672; margin-bottom: 15px;">💡 使用说明</h4>
          <p style="margin: 0; opacity: 0.9;">每个组件都包含完整的 HTML、CSS/SCSS 和 JavaScript 代码，可以直接复制使用或根据需要进行修改。所有组件都经过响应式设计优化，支持移动端和桌面端。</p>
        </div>
      </section>

      <section class="component-section" id="component-1">
        <h2>1. FAQ 手风琴</h2>
        <p>一个标准的问答(FAQ)区域，使用 Bootstrap 的手风琴（Accordion）组件实现展开和折叠功能。</p>
        <h4>CSS</h4>
        <pre><code class="language-css"><style>
  main .part-faq .accordion-item {
    padding: 2rem 0;
    border-bottom: 1px solid #e2e2e2;
  }

  main .part-faq .accordion-item [aria-expanded="true"] .faq-title {
    font-size: 1.5rem;
    font-weight: 700;
  }

  main .part-faq .accordion-item [aria-expanded="true"] svg {
    transform: rotate(180deg);
  }

  main .part-faq .accordion-item .faq-title {
    display: flex;
    align-items: center;
    justify-content: left;
    gap: 8px;
    flex-shrink: 0;
    max-width: 90%;
    font-weight: 500;
    font-size: 1.125rem;
  }
</style></code></pre>
        <h4>HTML</h4>
        <pre><code class="language-html">
    &lt;section class=&quot;part-faq py-5&quot; id=&quot;part-faq&quot;&gt;
&lt;div class=&quot;container mv-xl-5 mb-lg-3&quot;&gt;
  &lt;h2 class=&quot;pb-xl-5 pb-4&quot;&gt;See What Our Users Ask Frequently&lt;/h2&gt;
  &lt;div class=&quot;row justify-content-center&quot;&gt;
    &lt;div class=&quot;col-md-10&quot;&gt;
      &lt;div class=&quot;accordion&quot; id=&quot;accordionExample&quot;&gt;
        &lt;div class=&quot;accordion-item&quot;&gt;
          &lt;div
            class=&quot;d-flex align-items-center justify-content-between with-hand collapsed&quot;
            id=&quot;headingOne&quot;
            data-toggle=&quot;collapse &quot;
            data-target=&quot;#collapseOne&quot;
            aria-expanded=&quot;true&quot;
            aria-controls=&quot;collapseOne&quot;&gt;
            &lt;div class=&quot;faq-title&quot;&gt;
              &lt;img loading=&quot;lazy&quot; src=&quot;https://images.wondershare.com/drfone/images2025/business/question-icon.svg&quot; alt=&quot;question&quot; width=&quot;24&quot; /&gt;

              &lt;div class=&quot;title-desc&quot;&gt;Is PDF password removal with DocPassRemover secure?&lt;/div&gt;
            &lt;/div&gt;
            &lt;i&gt;
              &lt;svg width=&quot;24&quot; height=&quot;24&quot; viewBox=&quot;0 0 24 24&quot; fill=&quot;none&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;&gt;
                &lt;path d=&quot;M6 9L12 15L18 9&quot; stroke=&quot;currentColor&quot; stroke-width=&quot;2&quot;&gt;&lt;/path&gt;
              &lt;/svg&gt;
            &lt;/i&gt;
          &lt;/div&gt;
          &lt;div id=&quot;collapseOne&quot; class=&quot;collapse show&quot; aria-labelledby=&quot;headingOne&quot; data-parent=&quot;#accordionExample&quot; style=&quot;&quot;&gt;
            &lt;div class=&quot;pt-3&quot;&gt;
              Absolutely. All processes run locally on your computer—no need to upload files online, ensuring total privacy and data protection.
            &lt;/div&gt;
          &lt;/div&gt;
        &lt;/div&gt;
        &lt;div class=&quot;accordion-item&quot;&gt;
          &lt;div
            class=&quot;d-flex align-items-center justify-content-between with-hand collapsed&quot;
            id=&quot;headingTwo&quot;
            data-toggle=&quot;collapse&quot;
            data-target=&quot;#collapseTwo&quot;
            aria-expanded=&quot;false&quot;
            aria-controls=&quot;collapseTwo&quot;&gt;
            &lt;div class=&quot;faq-title&quot;&gt;
              &lt;img loading=&quot;lazy&quot; src=&quot;https://images.wondershare.com/drfone/images2025/business/question-icon.svg&quot; alt=&quot;question&quot; width=&quot;24&quot; /&gt;

              &lt;div class=&quot;title-desc&quot;&gt;Will my original PDF file be changed during unlocking?&lt;/div&gt;
            &lt;/div&gt;
            &lt;i&gt;
              &lt;svg width=&quot;24&quot; height=&quot;24&quot; viewBox=&quot;0 0 24 24&quot; fill=&quot;none&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;&gt;
                &lt;path d=&quot;M6 9L12 15L18 9&quot; stroke=&quot;currentColor&quot; stroke-width=&quot;2&quot;&gt;&lt;/path&gt;
              &lt;/svg&gt;
            &lt;/i&gt;
          &lt;/div&gt;
          &lt;div id=&quot;collapseTwo&quot; class=&quot;collapse&quot; aria-labelledby=&quot;headingTwo&quot; data-parent=&quot;#accordionExample&quot; style=&quot;&quot;&gt;
            &lt;div class=&quot;pt-3&quot;&gt;Not at all. Your original file stays intact—decrypted without data loss or corruption.&lt;/div&gt;
          &lt;/div&gt;
        &lt;/div&gt;
        &lt;div class=&quot;accordion-item&quot;&gt;
          &lt;div
            class=&quot;d-flex align-items-center justify-content-between with-hand&quot;
            id=&quot;headingThree&quot;
            data-toggle=&quot;collapse&quot;
            data-target=&quot;#collapseThree&quot;
            aria-expanded=&quot;true&quot;
            aria-controls=&quot;collapseThree&quot;&gt;
            &lt;div class=&quot;faq-title&quot;&gt;
              &lt;img loading=&quot;lazy&quot; src=&quot;https://images.wondershare.com/drfone/images2025/business/question-icon.svg&quot; alt=&quot;question&quot; width=&quot;24&quot; /&gt;

              &lt;div class=&quot;title-desc&quot;&gt;What if the PDF decryption fails?&lt;/div&gt;
            &lt;/div&gt;
            &lt;i&gt;
              &lt;svg width=&quot;24&quot; height=&quot;24&quot; viewBox=&quot;0 0 24 24&quot; fill=&quot;none&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;&gt;
                &lt;path d=&quot;M6 9L12 15L18 9&quot; stroke=&quot;currentColor&quot; stroke-width=&quot;2&quot;&gt;&lt;/path&gt;
              &lt;/svg&gt;
            &lt;/i&gt;
          &lt;/div&gt;
          &lt;div id=&quot;collapseThree&quot; class=&quot;collapse&quot; aria-labelledby=&quot;headingThree&quot; data-parent=&quot;#accordionExample&quot; style=&quot;&quot;&gt;
            &lt;div class=&quot;pt-3&quot;&gt;You only pay when it works. No unlock, no charge—guaranteed.&lt;/div&gt;
          &lt;/div&gt;
        &lt;/div&gt;
        &lt;div class=&quot;accordion-item&quot;&gt;
          &lt;div
            class=&quot;d-flex align-items-center justify-content-between with-hand collapsed&quot;
            id=&quot;headingFour&quot;
            data-toggle=&quot;collapse&quot;
            data-target=&quot;#collapseFour&quot;
            aria-expanded=&quot;false&quot;
            aria-controls=&quot;collapseFour&quot;&gt;
            &lt;div class=&quot;faq-title&quot;&gt;
              &lt;img loading=&quot;lazy&quot; src=&quot;https://images.wondershare.com/drfone/images2025/business/question-icon.svg&quot; alt=&quot;question&quot; width=&quot;24&quot; /&gt;

              &lt;div class=&quot;title-desc&quot;&gt;What encryption levels and PDF formats are supported?&lt;/div&gt;
            &lt;/div&gt;
            &lt;i&gt;
              &lt;svg width=&quot;24&quot; height=&quot;24&quot; viewBox=&quot;0 0 24 24&quot; fill=&quot;none&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;&gt;
                &lt;path d=&quot;M6 9L12 15L18 9&quot; stroke=&quot;currentColor&quot; stroke-width=&quot;2&quot;&gt;&lt;/path&gt;
              &lt;/svg&gt;
            &lt;/i&gt;
          &lt;/div&gt;
          &lt;div id=&quot;collapseFour&quot; class=&quot;collapse&quot; aria-labelledby=&quot;headingFour&quot; data-parent=&quot;#accordionExample&quot; style=&quot;&quot;&gt;
            &lt;div class=&quot;pt-3&quot;&gt;
              The tool supports 128-bit &amp; 256-bit AES, and 128-bit RC4 encryption. Compatible with Adobe PDF versions 1.0 to 2.0—ensuring seamless
              unlocking for both old and new PDF files.
            &lt;/div&gt;
          &lt;/div&gt;
        &lt;/div&gt;
        &lt;div class=&quot;accordion-item&quot;&gt;
          &lt;div
            class=&quot;d-flex align-items-center justify-content-between with-hand collapsed&quot;
            id=&quot;headingFive&quot;
            data-toggle=&quot;collapse&quot;
            data-target=&quot;#collapseFive&quot;
            aria-expanded=&quot;false&quot;
            aria-controls=&quot;collapseFive&quot;&gt;
            &lt;div class=&quot;faq-title&quot;&gt;
              &lt;img loading=&quot;lazy&quot; src=&quot;https://images.wondershare.com/drfone/images2025/business/question-icon.svg&quot; alt=&quot;question&quot; width=&quot;24&quot; /&gt;

              &lt;div class=&quot;title-desc&quot;&gt;Do I need Adobe Acrobat to unlock a PDF?&lt;/div&gt;
            &lt;/div&gt;
            &lt;i&gt;
              &lt;svg width=&quot;24&quot; height=&quot;24&quot; viewBox=&quot;0 0 24 24&quot; fill=&quot;none&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;&gt;
                &lt;path d=&quot;M6 9L12 15L18 9&quot; stroke=&quot;currentColor&quot; stroke-width=&quot;2&quot;&gt;&lt;/path&gt;
              &lt;/svg&gt;
            &lt;/i&gt;
          &lt;/div&gt;
          &lt;div id=&quot;collapseFive&quot; class=&quot;collapse&quot; aria-labelledby=&quot;headingFive&quot; data-parent=&quot;#accordionExample&quot; style=&quot;&quot;&gt;
            &lt;div class=&quot;pt-3&quot;&gt;No. DocPassRemover works on its own—you can unlock PDFs without installing Adobe Acrobat or any other app.&lt;/div&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/div&gt;
&lt;/section&gt;
</code></pre>
      </section>

      <section class="component-section" id="component-2">
        <h2>2. 手风琴与 Swiper 联动组件</h2>
        <p>一个左侧手风琴导航，右侧 Swiper 轮播的联动组件。点击手风琴项目时，右侧轮播会切换到对应内容。</p>

        <!-- 实际组件演示 -->
        <div style="margin: 2rem 0; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
          <h3 style="margin-bottom: 1rem; color: var(--accent-purple);">组件演示</h3>
          <!-- Feature Accordion Swiper Component -->
          <div class="feature-container">
            <!-- Left Side: Accordion Controls -->
            <div class="feature-text-wrapper">
              <h3 class="feature-title">组件主标题</h3>
              <div id="feature-accordion" class="feature-accordion">
                <!-- Accordion Item 1 -->
                <div class="feature-accordion-item">
                  <div id="heading-1" class="feature-accordion-header" data-toggle="collapse" data-target="#collapse-1" aria-expanded="true" aria-controls="collapse-1">
                    <div class="accordion-title">特性标题 1</div>
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M6 9L12 15L18 9" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                  </div>
                  <div id="collapse-1" class="collapse show" aria-labelledby="heading-1" data-parent="#feature-accordion">
                    <p class="accordion-content">这里是特性 1 的详细内容描述，用于解释说明该项的具体功能和优势。</p>
                  </div>
                </div>
                <!-- Accordion Item 2 -->
                <div class="feature-accordion-item">
                  <div id="heading-2" class="feature-accordion-header collapsed" data-toggle="collapse" data-target="#collapse-2" aria-expanded="false" aria-controls="collapse-2">
                    <div class="accordion-title">特性标题 2</div>
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M6 9L12 15L18 9" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                  </div>
                  <div id="collapse-2" class="collapse" aria-labelledby="heading-2" data-parent="#feature-accordion">
                    <p class="accordion-content">这里是特性 2 的详细内容描述，用于解释说明该项的具体功能和优势。</p>
                  </div>
                </div>
                <!-- Accordion Item 3 -->
                <div class="feature-accordion-item">
                  <div id="heading-3" class="feature-accordion-header collapsed" data-toggle="collapse" data-target="#collapse-3" aria-expanded="false" aria-controls="collapse-3">
                    <div class="accordion-title">特性标题 3</div>
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M6 9L12 15L18 9" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                  </div>
                  <div id="collapse-3" class="collapse" aria-labelledby="heading-3" data-parent="#feature-accordion">
                    <p class="accordion-content">这里是特性 3 的详细内容描述，用于解释说明该项的具体功能和优势。</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Right Side: Swiper Display -->
            <div class="feature-media-wrapper">
              <div id="feature-swiper" class="swiper">
                <div class="swiper-wrapper">
                  <div class="swiper-slide">
                    <img src="https://placehold.co/600x450/334054/FFF?text=Display+1" alt="Display Media 1" class="img-fluid" />
                  </div>
                  <div class="swiper-slide">
                    <img src="https://placehold.co/600x450/54334f/FFF?text=Display+2" alt="Display Media 2" class="img-fluid" />
                  </div>
                  <div class="swiper-slide">
                    <img src="https://placehold.co/600x450/33543e/FFF?text=Display+3" alt="Display Media 3" class="img-fluid" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <h4>HTML</h4>
        <pre><code class="language-html">&lt;!-- Feature Accordion Swiper Component --&gt;
&lt;div class="feature-container"&gt;
  &lt;!-- Left Side: Accordion Controls --&gt;
  &lt;div class="feature-text-wrapper"&gt;
    &lt;h3 class="feature-title"&gt;组件主标题&lt;/h3&gt;
    &lt;div id="feature-accordion" class="feature-accordion"&gt;
      &lt;!-- Accordion Item 1 --&gt;
      &lt;div class="feature-accordion-item"&gt;
        &lt;div id="heading-1" class="feature-accordion-header" data-toggle="collapse" data-target="#collapse-1" aria-expanded="true" aria-controls="collapse-1"&gt;
          &lt;div class="accordion-title"&gt;特性标题 1&lt;/div&gt;
          &lt;svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"&gt;
            &lt;path d="M6 9L12 15L18 9" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" /&gt;
          &lt;/svg&gt;
        &lt;/div&gt;
        &lt;div id="collapse-1" class="collapse show" aria-labelledby="heading-1" data-parent="#feature-accordion"&gt;
          &lt;p class="accordion-content"&gt;这里是特性 1 的详细内容描述，用于解释说明该项的具体功能和优势。&lt;/p&gt;
        &lt;/div&gt;
      &lt;/div&gt;
      &lt;!-- 更多手风琴项目... --&gt;
    &lt;/div&gt;
  &lt;/div&gt;
  &lt;!-- Right Side: Swiper Display --&gt;
  &lt;div class="feature-media-wrapper"&gt;
    &lt;div id="feature-swiper" class="swiper"&gt;
      &lt;div class="swiper-wrapper"&gt;
        &lt;div class="swiper-slide"&gt;
          &lt;img src="https://placehold.co/600x450/334054/FFF?text=Display+1" alt="Display Media 1" class="img-fluid" /&gt;
        &lt;/div&gt;
        &lt;!-- 更多轮播项目... --&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/div&gt;</code></pre>
        <h4>SCSS</h4>
        <pre><code class="language-scss">/* SCSS Variables for Easy Customization */
$component-bg: #1e1f22;
$component-padding: 2.5rem;
$component-padding-mobile: 1.5rem 1rem;
$border-radius: 1.5rem;
$gap-size: 1.75rem;

$item-padding: 0.75rem 1.5rem;
$item-padding-active: 1.5625rem;
$item-radius: 0.5rem;
$item-border-color: #393b3f;
$item-active-bg: linear-gradient(86.47deg, rgba(4, 88, 255, 0.2) 1.47%, rgba(4, 153, 255, 0.2) 96.84%);
$item-active-border: linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%);

$title-font-size: 2rem;
$title-font-weight: 600;
$accordion-title-font-weight: 600;
$accordion-content-font-size: 14px;
$accordion-content-margin-top: 8px;

/* Component Styles */
.feature-container {
  background-color: $component-bg;
  border-radius: $border-radius;
  padding: $component-padding;
  display: flex;
  gap: $gap-size;
  overflow: hidden;

  @media (max-width: 992px) {
    flex-direction: column;
  }
  @media (max-width: 576px) {
    padding: $component-padding-mobile;
  }

  .feature-text-wrapper {
    flex: 1;
    max-width: 482px;
    margin: 0 auto;
    @media (max-width: 992px) {
      max-width: 100%;
      width: 100%;
    }

    .feature-title {
      font-weight: $title-font-weight;
      font-size: $title-font-size;
      text-align: left;
      color: #fff;
      margin-bottom: 1.5rem;
      @media (max-width: 992px) {
        text-align: center;
      }
    }
  }

  .feature-accordion {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;

    .feature-accordion-item {
      border-radius: $item-radius;
      overflow: hidden;
      padding: $item-padding;
      position: relative;
      color: #fff;

      svg {
        transition: transform 0.3s ease-in-out;
      }

      &::after {
        content: "";
        position: absolute;
        inset: 0;
        padding: 1px;
        border-radius: $item-radius;
        background: $item-border-color;
        mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
        mask-composite: exclude;
        pointer-events: none;
      }

      /* Active State Styling */
      &:has([aria-expanded="true"]) {
        padding-top: $item-padding-active;
        padding-bottom: $item-padding-active;
        background: $item-active-bg;
        &::after {
          background: $item-active-border;
        }
        svg {
          transform: rotate(-180deg);
        }
      }

      .feature-accordion-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        line-height: 1;
        gap: 0.5rem;
        cursor: pointer;

        .accordion-title {
          font-weight: $accordion-title-font-weight;
        }
      }

      .accordion-content {
        font-size: $accordion-content-font-size;
        margin-top: $accordion-content-margin-top;
        color: #f1f1f1;
        padding-right: 1.5rem; // Avoid text overlapping with icon
      }
    }
  }

  .feature-media-wrapper {
    width: 50%;
    flex-shrink: 0;
    @media (max-width: 992px) {
      width: 100%;
      order: -1; // Move image to the top on mobile
      margin-bottom: 1.5rem;
    }

    .swiper-slide {
      border-radius: $item-radius;
      overflow: hidden;
      img, video {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
}</code></pre>
        <h4>JavaScript</h4>
        <pre><code class="language-javascript">$(document).ready(function () {
  // Initialize Swiper 7
  const featureSwiper = new Swiper("#feature-swiper", {
    effect: "fade",
    fadeEffect: {
      crossFade: true,
    },
    // Disable all user interactions
    allowTouchMove: false,
    noSwiping: true,
    simulateTouch: false,
    keyboard: {
      enabled: false
    },
  });

  // Link accordion clicks to Swiper slides
  $("#feature-accordion .feature-accordion-header").each(function (index) {
    $(this).on("click", function () {
      // When an accordion header is clicked, slide Swiper to the corresponding index
      featureSwiper.slideTo(index);
    });
  });
});</code></pre>
      </section>

      <section class="component-section" id="component-3">
        <h2>3. PC卡片与Mobile轮播</h2>
        <p>一个响应式组件，在桌面端显示为网格布局的卡片，鼠标悬停时显示详细信息；在移动端则自动转换为可滑动的 Swiper 轮播。</p>
        <h4>CSS</h4>
        <pre><code class="language-css"><style>
  @media (min-width: 992px) {
    #swiper-tips .swiper-wrapper {
      gap: 1.875rem;
      flex-wrap: wrap;
    }

    #swiper-tips .swiper-wrapper .swiper-slide {
      flex: 1 1 calc(33% - 1.875rem);
    }
  }

  .tip-item {
    border-radius: 2rem;
    position: relative;
    overflow: hidden;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    padding: 3rem 2rem;
    color: #000;
    z-index: 3;
    transition: all 0.2s;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    background-color: #fff;
  }

  .tip-item:hover {
    box-shadow: 0px 0px 12px 0px #00d1ff4d;
  }

  .tip-item:hover::after {
    content: "";
    position: absolute;
    inset: 0;
    border-radius: 2rem;
    padding: 2px;
    background: linear-gradient(86.47deg, #0458ff 1.47%, #0499ff 96.84%), linear-gradient(127.35deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 36.09%);
    mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
    -webkit-mask-image: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
    mask-composite: exclude;
  }

  .tip-item:hover .text-detail {
    top: 2px;
  }

  .tip-item .tip-icon {
    height: 6rem;
    width: 6rem;
  }

  .tip-item .text-detail {
    position: absolute;
    width: calc(100% - 4px);
    height: calc(100% - 4px);
    padding: 0rem 2rem;
    display: flex;
    justify-content: center;
    flex-direction: column;
    z-index: 2;
    border-radius: 2rem;
    left: 2px;
    top: 100%;
    overflow: hidden;
    background-image: url(https://images.wondershare.com/repairit/images2025/PPT-repair/tip-card-bg.svg) !important;
    transition: all 0.3s;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }
</style></code></pre>
        <h4>HTML</h4>
        <pre><code class="language-html">
          &lt;div class=&quot;swiper py-4&quot; id=&quot;swiper-tips&quot;&gt;
          &lt;div class=&quot;swiper-wrapper&quot;&gt;
            &lt;div class=&quot;swiper-slide h-auto&quot;&gt;
              &lt;div class=&quot;tip-item&quot;&gt;
                &lt;div class=&quot;tip-icon mb-2&quot;&gt;
                  &lt;img loading=&quot;lazy&quot; src=&quot;https://images.wondershare.com/repairit/images2025/PDF-repair/use-trusted-tools.svg&quot; alt=&quot;Reliable&quot; class=&quot;img-fluid&quot; /&gt;
                &lt;/div&gt;
                &lt;div class=&quot;font-weight-extra-bold font-size-large&quot;&gt;Use Trusted Tools&lt;/div&gt;
                &lt;div class=&quot;text-detail&quot;&gt;
                  &lt;div class=&quot;font-weight-extra-bold font-size-extra mb-2&quot;&gt;Use Trusted Tools&lt;/div&gt;
                  &lt;p class=&quot;opacity-7&quot;&gt;Open and edit PDFs using reliable and well-known software. Untrusted tools might break your file or add hidden problems.&lt;/p&gt;
                &lt;/div&gt;
              &lt;/div&gt;
            &lt;/div&gt;
            &lt;div class=&quot;swiper-slide h-auto&quot;&gt;
              &lt;div class=&quot;tip-item&quot;&gt;&lt;/div&gt;
            &lt;/div&gt;
          &lt;/div&gt;
          &lt;div class=&quot;swiper-pagination&quot; style=&quot;bottom: 0&quot;&gt;&lt;/div&gt;
        &lt;/div&gt;  
        </code></pre>
      </section>

      <section class="component-section" id="component-4">
        <h2>4. 页脚产品推广盒</h2>
        <p>一个带有背景图、Logo、标题和行动号召（Call-to-Action）按钮组的页脚推广区域。</p>
        <h4>CSS</h4>
        <pre><code class="language-css"><style>
  main .part-footer .btn-wrapper .btn-white {
    color: #0274ff;
  }

  main .part-footer .btn-wrapper .btn-outline-white:hover {
    color: #0274ff;
  }

  main .part-footer .footer-box {
    margin: 0 4rem;
    border-radius: 1.5rem;
    background: url(https://images.wondershare.com/drfone/images2025/DocPassRemover/footer-bg.jpg) no-repeat center center/cover;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    color: #fff;
    padding: 6rem 0;
    color: #fff;
  }

  @media (max-width: 768px) {
    main .part-footer .footer-box {
      padding: 2.5rem 1rem;
      margin: 0 15px;
      text-align: center;
    }
  }

  main .part-footer .footer-box .btn {
    min-width: 192px;
  }
</style></code></pre>
        <h4>HTML</h4>
        <pre><code class="language-html">
          &lt;section class=&quot;part-footer py-5&quot; id=&quot;part-footer&quot;&gt;
          &lt;div class=&quot;footer-box my-xl-5 my-lg-3&quot;&gt;
            &lt;img
              loading=&quot;lazy&quot;
              src=&quot;https://images.wondershare.com/drfone/images2025/DocPassRemover/DrFone-DocPassRemover-logo.svg&quot;
              alt=&quot;drfone&quot;
              style=&quot;height: 4.5rem&quot; /&gt;
            &lt;h2 class=&quot;display-3 font-weight-bold text-white my-3 pt-md-4&quot;&gt;Take Back Control of Your PDFs&lt;/h2&gt;
            &lt;p class=&quot;font-size-extra mb-4 pb-xl-2 text-white&quot;&gt;Remove password locks and unlock full access—instantly.&lt;/p&gt;
            &lt;div class=&quot;btn-wrapper&quot;&gt;
              &lt;a href=&quot;#&quot; class=&quot;btn btn-lg btn-white sys-win&quot;
                &gt;&lt;i class=&quot;wsc-icon wsc-icon-loaded&quot; data-icon=&quot;brand-windows&quot;
                  &gt;&lt;svg class=&quot;wsc-svg-brand-windows&quot; width=&quot;48&quot; height=&quot;48&quot; viewBox=&quot;0 0 48 48&quot; fill=&quot;none&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;&gt;
                    &lt;path
                      d=&quot;M0 6.61071L19.6714 3.9V22.9071H0V6.61071ZM0 41.3893L19.6714 44.1V25.3286H0V41.3893V41.3893ZM21.8357 44.3893L48 48V25.3286H21.8357V44.3893V44.3893ZM21.8357 3.61071V22.9071H48V0L21.8357 3.61071V3.61071Z&quot;
                      fill=&quot;currentColor&quot;&gt;&lt;/path&gt;
                  &lt;/svg&gt;
                &lt;/i&gt;
                Try It Free&lt;/a
              &gt;
              &lt;a href=&quot;#&quot; class=&quot;btn btn-lg btn-white sys-mac&quot;
                &gt;&lt;i class=&quot;wsc-icon wsc-icon-loaded&quot; data-icon=&quot;brand-windows&quot;
                  &gt;&lt;svg class=&quot;wsc-svg-brand-windows&quot; width=&quot;48&quot; height=&quot;48&quot; viewBox=&quot;0 0 48 48&quot; fill=&quot;none&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;&gt;
                    &lt;path
                      d=&quot;M0 6.61071L19.6714 3.9V22.9071H0V6.61071ZM0 41.3893L19.6714 44.1V25.3286H0V41.3893V41.3893ZM21.8357 44.3893L48 48V25.3286H21.8357V44.3893V44.3893ZM21.8357 3.61071V22.9071H48V0L21.8357 3.61071V3.61071Z&quot;
                      fill=&quot;currentColor&quot;&gt;&lt;/path&gt;
                  &lt;/svg&gt;
                &lt;/i&gt;
                Try It Free&lt;/a
              &gt;
              &lt;a href=&quot;#&quot; class=&quot;btn btn-lg btn-white sys-ios&quot;&gt;Try It Free&lt;/a&gt;
              &lt;a href=&quot;#&quot; class=&quot;btn btn-lg btn-white sys-android&quot;&gt;Try It Free&lt;/a&gt;
              &lt;a href=&quot;#&quot; target=&quot;_blank&quot; class=&quot;btn btn-lg btn-outline-white sys-win&quot;&gt;Buy Now&lt;/a&gt;
              &lt;a href=&quot;#&quot; target=&quot;_blank&quot; class=&quot;btn btn-lg btn-outline-white sys-mac&quot;&gt;Buy Now&lt;/a&gt;
              &lt;a href=&quot;#&quot; target=&quot;_blank&quot; class=&quot;btn btn-lg btn-outline-white dev-mobile&quot;&gt;Buy Now&lt;/a&gt;
            &lt;/div&gt;
          &lt;/div&gt;
        &lt;/section&gt;</code></pre>
      </section>

      <section class="component-section" id="component-5">
        <h2>5. 无限滚动走马灯</h2>
        <p>通过 CSS <code>animation</code> 实现的无缝循环滚动效果，常用于展示合作伙伴 Logo 墙。</p>
        <h4>CSS</h4>
        <pre><code class="language-css"><style>
  @keyframes marquee1 {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(-50%);
    }
  }
  @keyframes marquee2 {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(50%);
    }
  }
  .top-imgList,
  .bottom-imgList {
    display: flex;
    flex-wrap: nowrap;
    /* gap: 2.375rem; */
    /* 这里使用gap会有些问题，0%和50%时的位置不太一样 */
    width: fit-content;
    .logo-line {
      height: 4.25rem;
      max-width: fit-content;
      margin: 0 1.125rem;
      /* 每个item各自给间隔一半的margin */
    }
  }
  .top-imgList {
    animation: marquee1 30s linear infinite;
  }
  .bottom-imgList {
    animation: marquee2 30s linear infinite;
  }
</style></code></pre>
        <h4>HTML</h4>
        <pre><code class="language-html">
          &lt;div class=&quot;top-imgList&quot;&gt;
          &lt;!-- first --&gt;
          &lt;img src=&quot;./images/logo-line-horizontal.png&quot; alt=&quot;logo-line&quot; class=&quot;logo-line&quot; /&gt;
          &lt;!-- copy --&gt;
          &lt;img src=&quot;./images/logo-line-horizontal.png&quot; alt=&quot;logo-line&quot; class=&quot;logo-line&quot; /&gt;
        &lt;/div&gt;
        &lt;div class=&quot;bottom-imgList mt-3&quot;&gt;
          &lt;!-- first --&gt;
          &lt;img src=&quot;./images/logo-line-horizontal.png&quot; alt=&quot;logo-line&quot; class=&quot;logo-line&quot; /&gt;
          &lt;!-- copy --&gt;
          &lt;img src=&quot;./images/logo-line-horizontal.png&quot; alt=&quot;logo-line&quot; class=&quot;logo-line&quot; /&gt;
        &lt;/div&gt; 
        </code></pre>
      </section>

      <section class="component-section" id="component-6">
        <h2>6. 纯 Tab 切换</h2>
        <p>基于 Bootstrap <code>data</code> 属性实现的简单标签页内容切换功能。</p>
        <h4>HTML</h4>
        <pre><code class="language-html"><!-- 纯tab切换 -->
          &lt;nav class=&quot;nav mt-3&quot; role=&quot;tablist&quot;&gt;
          &lt;span class=&quot;nav-item active&quot; id=&quot;item-1-tab&quot; data-toggle=&quot;tab&quot; data-target=&quot;#item-1&quot; role=&quot;tab&quot; aria-controls=&quot;item-1&quot; aria-selected=&quot;true&quot;
            &gt;Video Formats&lt;/span
          &gt;
          &lt;span class=&quot;nav-item&quot; id=&quot;item-2-tab&quot; data-toggle=&quot;tab&quot; data-target=&quot;#item-2&quot; role=&quot;tab&quot; aria-controls=&quot;item-2&quot; aria-selected=&quot;false&quot;&gt;Video Devices&lt;/span&gt;
          &lt;span class=&quot;nav-item&quot; id=&quot;item-3-tab&quot; data-toggle=&quot;tab&quot; data-target=&quot;#item-3&quot; role=&quot;tab&quot; aria-controls=&quot;item-3&quot; aria-selected=&quot;false&quot;
            &gt;Corruption Scenarios&lt;/span
          &gt;
        &lt;/nav&gt;
        &lt;div class=&quot;tab-content&quot;&gt;
          &lt;div class=&quot;tab-pane fade show active&quot; id=&quot;item-1&quot; role=&quot;tabpanel&quot; aria-labelledby=&quot;item-1-tab&quot;&gt;1&lt;/div&gt;
          &lt;div class=&quot;tab-pane fade&quot; id=&quot;item-2&quot; role=&quot;tabpanel&quot; aria-labelledby=&quot;item-2-tab&quot;&gt;2&lt;/div&gt;
          &lt;div class=&quot;tab-pane fade&quot; id=&quot;item-3&quot; role=&quot;tabpanel&quot; aria-labelledby=&quot;item-3-tab&quot;&gt;3&lt;/div&gt;
        &lt;/div&gt;</code></pre>
      </section>

      <section class="component-section" id="component-7">
        <h2>7. Tab 与 Swiper 联动轮播</h2>
        <p>将 Tab 导航与 Swiper 轮播结合，实现点击 Tab 切换到对应 Slide，同时 Swiper 自动轮播时高亮相应 Tab 的效果。</p>
        <h4>HTML</h4>
        <pre><code class="language-html">
          &lt;nav class=&quot;tab-list py-md-2&quot;&gt;
          &lt;div class=&quot;tab-item&quot;&gt;Edit Encrypted Documents&lt;/div&gt;
          &lt;div class=&quot;tab-item&quot;&gt;Fill and Save Forms&lt;/div&gt;
          &lt;div class=&quot;tab-item&quot;&gt;Copy Text and Images&lt;/div&gt;
          &lt;div class=&quot;tab-item&quot;&gt;Print Without Restrictions&lt;/div&gt;
        &lt;/nav&gt;
        &lt;div class=&quot;swiper&quot; id=&quot;swiper-unlock&quot;&gt;
          &lt;div class=&quot;swiper-wrapper py-4&quot;&gt;
            &lt;div class=&quot;swiper-slide h-auto&quot;&gt;&lt;/div&gt;
            &lt;div class=&quot;swiper-slide h-auto&quot;&gt;&lt;/div&gt;
            &lt;div class=&quot;swiper-slide h-auto&quot;&gt;&lt;/div&gt;
            &lt;div class=&quot;swiper-slide h-auto&quot;&gt;&lt;/div&gt;
          &lt;/div&gt;
        &lt;/div&gt;
</code></pre>
        <h4>JavaScript</h4>
        <pre><code class="language-javascript"><script>
  const swiperUnlock = new Swiper("#swiper-unlock", {
    slidesPerView: 1,
    spaceBetween: 30,
    loop: true,
    loopedSlides: 2,
    autoplay: {
      delay: 3000,
      disableOnInteraction: false,
    },
    pagination: {
      el: ".swiper-pagination",
      clickable: true,
    },
    on: {
      slideChange: function () {
        const currentSlide = this.realIndex;
        $(".tab-item").removeClass("active").eq(currentSlide).addClass("active");
      },
    },
  });
  $(" .tab-item").on("click", function () {
    const currentSlide = $(this).index();
    swiperUnlock.slideToLoop(currentSlide);
  });
</script></code></pre>
      </section>

      <section class="component-section" id="component-8">
        <h2>8. 悬浮水波纹下载按钮</h2>
        <p>一个带有 CSS 动画水波纹扩散效果的悬浮图标按钮。</p>
        <h4>CSS</h4>
        <pre><code class="language-css"><style>
  @keyframes banner-diffuse1 {
    0% {
      transform: translate(-50%, -50%) scale(0.2);
      opacity: 0.1;
    }

    60% {
      transform: translate(-50%, -50%) scale(0.7);
      opacity: 0.5;
    }

    100% {
      transform: translate(-50%, -50%) scale(1);
      opacity: 0;
    }
  }

  @keyframes banner-diffuse2 {
    0% {
      transform: translate(-50%, -50%) scale(0.2);
      opacity: 0.1;
    }

    60% {
      transform: translate(-50%, -50%) scale(0.9);
      opacity: 0.5;
    }

    100% {
      transform: translate(-50%, -50%) scale(1);
      opacity: 0;
    }
  }
  .part-wave-icon-box {
    position: absolute;
    right: 59%;
    top: 6%;
    width: 6.25rem;
  }

  @media (max-width: 768px) {
    .part-wave-icon-box {
      width: 54px;
      right: 68%;
      top: -5%;
    }
  }

  .part-wave-icon-box .wave1 {
    width: 130%;
    aspect-ratio: 72 / 74;
    border-radius: 20%;
    border: 3px solid rgba(6, 94, 254, 0.5);
    z-index: 1;
    opacity: 0.8;
    backdrop-filter: blur(6px);
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%) scale(0);
    animation: banner-diffuse1 2s linear infinite;
  }

  .part-wave-icon-box .wave2 {
    width: 160%;
    aspect-ratio: 72 / 74;
    border-radius: 20%;
    border: 3px solid rgba(6, 94, 254, 0.5);
    z-index: 1;
    opacity: 0.8;
    backdrop-filter: blur(6px);
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%) scale(0);
    animation: banner-diffuse1 2s linear infinite;
  }
</style></code></pre>
        <h4>HTML</h4>
        <pre><code class="language-html">
          &lt;div class=&quot;part-wave-icon-box&quot;&gt;
          &lt;div class=&quot;wave1&quot;&gt;&lt;/div&gt;
          &lt;div class=&quot;wave2&quot;&gt;&lt;/div&gt;
          &lt;div class=&quot;position-relative&quot; style=&quot;z-index: 3&quot;&gt;
            &lt;a href=&quot;https://download.wondershare.com/drfone_docpassremover_full20274.exe&quot; class=&quot;download-box sys-win&quot;&gt;
              &lt;img src=&quot;https://images.wondershare.com/drfone/images2025/DocPassRemover/remove-icon.png&quot; alt=&quot;remove icon&quot; class=&quot;img-fluid&quot; /&gt;
            &lt;/a&gt;
            &lt;a href=&quot;https://download.wondershare.com/drfone_full3361.dmg&quot; class=&quot;download-box sys-mac&quot;&gt;
              &lt;img loading=&quot;lazy&quot; src=&quot;https://images.wondershare.com/drfone/images2025/DocPassRemover/remove-icon.png&quot; alt=&quot;remove icon&quot; class=&quot;img-fluid&quot; /&gt;
            &lt;/a&gt;
            &lt;a href=&quot;https://drfone.wondershare.com/mobile-download-guidance.html&quot; target=&quot;_blank&quot; class=&quot;download-box sys-android&quot;&gt;
              &lt;img loading=&quot;lazy&quot; src=&quot;https://images.wondershare.com/drfone/images2025/DocPassRemover/remove-icon.png&quot; alt=&quot;remove icon&quot; class=&quot;img-fluid&quot; /&gt;
            &lt;/a&gt;
            &lt;a href=&quot;https://drfone.wondershare.com/mobile-download-guidance.html&quot; target=&quot;_blank&quot; class=&quot;download-box sys-ios&quot;&gt;
              &lt;img loading=&quot;lazy&quot; src=&quot;https://images.wondershare.com/drfone/images2025/DocPassRemover/remove-icon.png&quot; alt=&quot;remove icon&quot; class=&quot;img-fluid&quot; /&gt;
            &lt;/a&gt;
          &lt;/div&gt;
        &lt;/div&gt; 
        </code></pre>
      </section>

      <section class="component-section" id="component-9">
        <h2>9. 高亮列对比表格</h2>
        <p>一个高度可定制的对比表格，通过 CSS 变量和特定类名实现某一列的视觉高亮，并带有突出的装饰条和圆角效果。</p>
        <h4>SCSS</h4>
        <pre><code class="language-scss"><style lang="scss">
  .table-wrapper {
    /* --- 1. 定制化变量 --- */
    --table-theme-color: #046fff; /* 主要主题色，用于高亮列 */
    --table-theme-color-border: #0085ff; /* 边框和外框颜色 */
    --table-side-col-bg: #e5f2ff; /* 侧边和头部非高亮背景色 */
    --table-main-bg: #f3f3f3; /* 表格主体背景色 */
    --table-text-primary: #000; /* 主要文字颜色 */
    --table-text-secondary: #fff; /* 次要文字颜色 (高亮列上) */
    --table-border-radius: 24px; /* 表格圆角大小 */
    --table-decorator-height: 1.5rem; /* 高亮列顶部和底部的装饰条高度 */

    /* --- 2. 基础布局和容器 --- */
    border-radius: var(--table-border-radius);
    width: 100%;
    position: relative;
    overflow: visible;
    background-color: var(--table-theme-color-border); /* 用背景色模拟外边框 */
    padding: 1px; /* 关键：让背景色显示为1px的边框 */
    margin-top: 5rem;
  }

  .inner-table {
    border-collapse: collapse; /* 合并边框 */
    border-style: hidden; /* 隐藏表格默认边框，由wrapper接管 */
    width: 100%;
    background: var(--table-main-bg);
    border-radius: var(--table-border-radius);
    overflow: visible; /* 必须设置，以显示高亮列的装饰条 */
  }

  /* --- 3. 单元格通用样式 --- */
  .inner-table th,
  .inner-table td {
    position: relative; /* 为伪元素定位提供基准 */
    padding: 1.5rem 1rem;
    text-align: center;
    vertical-align: middle;
    width: calc(100% / 6); /* 平均分配列宽，6是总列数 */
  }

  .inner-table th {
    font-weight: 700;
    font-size: 1rem;
    color: var(--table-text-primary);
    background-color: var(--table-side-col-bg);
  }

  .inner-table td {
    font-size: 0.875rem;
    color: rgba(0, 0, 0, 0.7);
  }

  /* 行标题列 (第一列) 的特殊样式 */
  .inner-table td:first-child {
    background-color: var(--table-side-col-bg);
    font-size: 1rem;
    font-weight: 700;
    color: var(--table-text-primary);
    padding: 1.5rem;
  }

  /* --- 4. 高亮列样式 --- */
  /* 这是核心！通过添加 .highlight-col 类来指定高亮列 */
  .inner-table .highlight-col {
    background-color: var(--table-theme-color);
    color: var(--table-text-secondary);
  }

  /* 高亮列顶部的装饰条 */
  .highlight-col-top::before {
    content: "";
    position: absolute;
    top: 0;
    left: -1px;
    width: calc(100% + 2px);
    height: var(--table-decorator-height);
    background-color: var(--table-theme-color);
    border-radius: 1rem 1rem 0 0;
    transform: translateY(-100%);
  }

  /* 高亮列底部的装饰条 */
  .highlight-col-bottom::before {
    content: "";
    position: absolute;
    bottom: 0;
    left: -1px;
    width: calc(100% + 2px);
    height: var(--table-decorator-height);
    background-color: var(--table-theme-color);
    border-radius: 0 0 1rem 1rem;
    transform: translateY(100%);
  }

  .mt-logo {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translate(-50%, -83%);
    width: 34%;
  }

  /* --- 5. 边框样式 --- */
  /* 垂直边框 */
  .inner-table th:not(:last-child),
  .inner-table td:not(:last-child) {
    border-right: 1px solid var(--table-theme-color-border);
  }

  /* 水平边框 (非高亮列) */
  .inner-table th:not(.highlight-col)::after,
  .inner-table tr:not(:last-child) td:not(.highlight-col)::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background-color: var(--table-theme-color-border);
  }

  /* 水平边框 (高亮列)，颜色稍浅 */
  .inner-table th.highlight-col::after,
  .inner-table tr:not(:last-child) td.highlight-col::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0.875rem;
    right: 0.875rem;
    height: 1px;
    background-color: rgba(255, 255, 255, 0.3); /* 白色半透明 */
  }

  /* --- 6. 内容对齐和响应式 --- */
  .inner-table td span {
    vertical-align: middle;
    margin-left: 8px;
  }

  /* 移动端响应式调整 */
  @media (max-width: 768px) {
    .inner-table th {
      font-size: 10px;
      padding: 8px 4px;
    }
    .inner-table td {
      font-size: 9px;
      padding: 8px 4px;
    }
    .inner-table td:first-child {
      font-size: 10px;
      padding: 8px;
    }
    .inner-table td img {
      width: 14px;
    }
    .mt-logo {
      width: 60%;
    }
  }
</style></code></pre>
        <h4>HTML</h4>
        <pre><code class="language-html">
          &lt;div class=&quot;table-wrapper&quot;&gt;
          &lt;table class=&quot;inner-table&quot;&gt;
            &lt;thead&gt;
              &lt;tr&gt;
                &lt;!-- 左上角圆角 --&gt;
                &lt;th style=&quot;border-top-left-radius: var(--table-border-radius)&quot;&gt;&lt;/th&gt;
        
                &lt;!-- 这是高亮列的表头。注意 &#39;highlight-col&#39; 和 &#39;highlight-col-top&#39; 类 --&gt;
                &lt;th class=&quot;highlight-col highlight-col-top&quot;&gt;
                  MobileTrans
                  &lt;span class=&quot;mt-logo&quot;&gt;
                    &lt;img
                      loading=&quot;lazy&quot;
                      src=&quot;https://mobiletrans.wondershare.com/images/images2025/video-transfer/MT-logo.svg&quot;
                      alt=&quot;mobiletrans logo&quot;
                      class=&quot;img-fluid&quot; /&gt;
                  &lt;/span&gt;
                &lt;/th&gt;
        
                &lt;th&gt;Email&lt;/th&gt;
                &lt;th&gt;Cloud&lt;/th&gt;
                &lt;th&gt;USB Cable&lt;/th&gt;
        
                &lt;!-- 右上角圆角 --&gt;
                &lt;th style=&quot;border-top-right-radius: var(--table-border-radius)&quot;&gt;Bluetooth&lt;/th&gt;
              &lt;/tr&gt;
            &lt;/thead&gt;
            &lt;tbody&gt;
              &lt;!-- 数据行 1 --&gt;
              &lt;tr&gt;
                &lt;!-- 行标题 --&gt;
                &lt;td&gt;Transfer Speed&lt;/td&gt;
                &lt;!-- 高亮列的数据单元格 --&gt;
                &lt;td class=&quot;highlight-col&quot;&gt;
                  &lt;img src=&quot;https://mobiletrans.wondershare.com/images/images2025/photo-transfer/green-right-icon.svg&quot; alt=&quot;right&quot; class=&quot;img-fluid&quot; /&gt;
                  &lt;span&gt;Fast &amp; Stable&lt;/span&gt;
                &lt;/td&gt;
                &lt;!-- 其他数据单元格 --&gt;
                &lt;td&gt;
                  &lt;img src=&quot;https://mobiletrans.wondershare.com/images/images2025/photo-transfer/yellow-warning-icon.svg&quot; alt=&quot;warning&quot; class=&quot;img-fluid&quot; /&gt;
                  &lt;span&gt;Depends on Internet&lt;/span&gt;
                &lt;/td&gt;
                &lt;td&gt;
                  &lt;img src=&quot;https://mobiletrans.wondershare.com/images/images2025/photo-transfer/yellow-warning-icon.svg&quot; alt=&quot;warning&quot; class=&quot;img-fluid&quot; /&gt;
                  &lt;span&gt;Depends on Internet&lt;/span&gt;
                &lt;/td&gt;
                &lt;td&gt;
                  &lt;img src=&quot;https://mobiletrans.wondershare.com/images/images2025/photo-transfer/green-right-icon.svg&quot; alt=&quot;right&quot; class=&quot;img-fluid&quot; /&gt;
                &lt;/td&gt;
                &lt;td&gt;
                  &lt;img src=&quot;https://mobiletrans.wondershare.com/images/images2025/photo-transfer/red-fault-icon.svg&quot; alt=&quot;fault&quot; class=&quot;img-fluid&quot; /&gt;
                  &lt;span&gt;Slow&lt;/span&gt;
                &lt;/td&gt;
              &lt;/tr&gt;
        
              &lt;!-- 数据行 2 --&gt;
              &lt;tr&gt;
                &lt;td&gt;Keep Photo Quality&lt;/td&gt;
                &lt;td class=&quot;highlight-col&quot;&gt;
                  &lt;img src=&quot;https://mobiletrans.wondershare.com/images/images2025/photo-transfer/green-right-icon.svg&quot; alt=&quot;right&quot; class=&quot;img-fluid&quot; /&gt;
                &lt;/td&gt;
                &lt;td&gt;
                  &lt;img src=&quot;https://mobiletrans.wondershare.com/images/images2025/photo-transfer/red-fault-icon.svg&quot; alt=&quot;fault&quot; class=&quot;img-fluid&quot; /&gt;
                &lt;/td&gt;
                &lt;td&gt;
                  &lt;img src=&quot;https://mobiletrans.wondershare.com/images/images2025/photo-transfer/green-right-icon.svg&quot; alt=&quot;right&quot; class=&quot;img-fluid&quot; /&gt;
                &lt;/td&gt;
                &lt;td&gt;
                  &lt;img src=&quot;https://mobiletrans.wondershare.com/images/images2025/photo-transfer/green-right-icon.svg&quot; alt=&quot;right&quot; class=&quot;img-fluid&quot; /&gt;
                &lt;/td&gt;
                &lt;td&gt;
                  &lt;img src=&quot;https://mobiletrans.wondershare.com/images/images2025/photo-transfer/green-right-icon.svg&quot; alt=&quot;right&quot; class=&quot;img-fluid&quot; /&gt;
                &lt;/td&gt;
              &lt;/tr&gt;
        
              &lt;!-- 最后一行 --&gt;
              &lt;tr&gt;
                &lt;!-- 左下角圆角 --&gt;
                &lt;td style=&quot;border-bottom-left-radius: var(--table-border-radius)&quot;&gt;No Limited Batch Transfer&lt;/td&gt;
                &lt;!-- 这是高亮列的最后一个单元格。注意 &#39;highlight-col&#39; 和 &#39;highlight-col-bottom&#39; 类 --&gt;
                &lt;td class=&quot;highlight-col highlight-col-bottom&quot;&gt;
                  &lt;img src=&quot;https://mobiletrans.wondershare.com/images/images2025/photo-transfer/green-right-icon.svg&quot; alt=&quot;right&quot; class=&quot;img-fluid&quot; /&gt;
                &lt;/td&gt;
                &lt;td&gt;
                  &lt;img src=&quot;https://mobiletrans.wondershare.com/images/images2025/photo-transfer/red-fault-icon.svg&quot; alt=&quot;fault&quot; class=&quot;img-fluid&quot; /&gt;
                &lt;/td&gt;
                &lt;td&gt;
                  &lt;img src=&quot;https://mobiletrans.wondershare.com/images/images2025/photo-transfer/red-fault-icon.svg&quot; alt=&quot;fault&quot; class=&quot;img-fluid&quot; /&gt;
                &lt;/td&gt;
                &lt;td&gt;
                  &lt;img src=&quot;https://mobiletrans.wondershare.com/images/images2025/photo-transfer/green-right-icon.svg&quot; alt=&quot;right&quot; class=&quot;img-fluid&quot; /&gt;
                &lt;/td&gt;
                &lt;!-- 右下角圆角 --&gt;
                &lt;td style=&quot;border-bottom-right-radius: var(--table-border-radius)&quot;&gt;
                  &lt;img src=&quot;https://mobiletrans.wondershare.com/images/images2025/photo-transfer/red-fault-icon.svg&quot; alt=&quot;fault&quot; class=&quot;img-fluid&quot; /&gt;
                &lt;/td&gt;
              &lt;/tr&gt;
            &lt;/tbody&gt;
          &lt;/table&gt;
        &lt;/div&gt;</code></pre>
      </section>

      <section class="component-section" id="component-10">
        <h2>10. 底部链接区域</h2>
        <p>一个常用的页脚链接区域组件，包含多列链接分组，适用于网站底部的导航和信息展示。</p>
        <h4>HTML</h4>
        <pre><code class="language-html">&lt;section class="footer-links py-5"&gt;
  &lt;div class="container"&gt;
    &lt;div class="row"&gt;
      &lt;div class="col-lg-3 col-md-6 mb-4"&gt;
        &lt;h5 class="footer-title"&gt;产品&lt;/h5&gt;
        &lt;ul class="footer-link-list"&gt;
          &lt;li&gt;&lt;a href="#"&gt;功能特性&lt;/a&gt;&lt;/li&gt;
          &lt;li&gt;&lt;a href="#"&gt;定价方案&lt;/a&gt;&lt;/li&gt;
          &lt;li&gt;&lt;a href="#"&gt;免费试用&lt;/a&gt;&lt;/li&gt;
          &lt;li&gt;&lt;a href="#"&gt;企业版&lt;/a&gt;&lt;/li&gt;
        &lt;/ul&gt;
      &lt;/div&gt;
      &lt;div class="col-lg-3 col-md-6 mb-4"&gt;
        &lt;h5 class="footer-title"&gt;支持&lt;/h5&gt;
        &lt;ul class="footer-link-list"&gt;
          &lt;li&gt;&lt;a href="#"&gt;帮助中心&lt;/a&gt;&lt;/li&gt;
          &lt;li&gt;&lt;a href="#"&gt;联系我们&lt;/a&gt;&lt;/li&gt;
          &lt;li&gt;&lt;a href="#"&gt;技术支持&lt;/a&gt;&lt;/li&gt;
          &lt;li&gt;&lt;a href="#"&gt;状态页面&lt;/a&gt;&lt;/li&gt;
        &lt;/ul&gt;
      &lt;/div&gt;
      &lt;div class="col-lg-3 col-md-6 mb-4"&gt;
        &lt;h5 class="footer-title"&gt;公司&lt;/h5&gt;
        &lt;ul class="footer-link-list"&gt;
          &lt;li&gt;&lt;a href="#"&gt;关于我们&lt;/a&gt;&lt;/li&gt;
          &lt;li&gt;&lt;a href="#"&gt;新闻动态&lt;/a&gt;&lt;/li&gt;
          &lt;li&gt;&lt;a href="#"&gt;招聘信息&lt;/a&gt;&lt;/li&gt;
          &lt;li&gt;&lt;a href="#"&gt;投资者关系&lt;/a&gt;&lt;/li&gt;
        &lt;/ul&gt;
      &lt;/div&gt;
      &lt;div class="col-lg-3 col-md-6 mb-4"&gt;
        &lt;h5 class="footer-title"&gt;法律&lt;/h5&gt;
        &lt;ul class="footer-link-list"&gt;
          &lt;li&gt;&lt;a href="#"&gt;隐私政策&lt;/a&gt;&lt;/li&gt;
          &lt;li&gt;&lt;a href="#"&gt;服务条款&lt;/a&gt;&lt;/li&gt;
          &lt;li&gt;&lt;a href="#"&gt;Cookie政策&lt;/a&gt;&lt;/li&gt;
          &lt;li&gt;&lt;a href="#"&gt;许可协议&lt;/a&gt;&lt;/li&gt;
        &lt;/ul&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/section&gt;</code></pre>
        <h4>CSS</h4>
        <pre><code class="language-css">.footer-links {
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.footer-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.footer-link-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-link-list li {
  margin-bottom: 0.5rem;
}

.footer-link-list a {
  color: #6c757d;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.footer-link-list a:hover {
  color: #007bff;
  text-decoration: none;
}

@media (max-width: 768px) {
  .footer-title {
    font-size: 1rem;
  }

  .footer-link-list a {
    font-size: 0.85rem;
  }
}</code></pre>
      </section>

      <hr />

      <section class="component-section" id="component-11">
        <h2>11. 通用全局样式 (SCSS) - 模块化拆分</h2>
        <p>这里将完整的 SCSS 文件拆分为多个独立的功能模块，方便按需复用。</p>

        <h3 id="component-11-1">11.1 全局重置与基础样式</h3>
        <p>包含 <code>box-sizing</code> 重置、<code>main</code> 标签基础背景色和字体色，以及标题、段落等元素的 <code>margin</code> 重置和一些辅助类。</p>
        <pre><code class="language-scss">// 通用样式设置
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

main {
  background-color: #fff;
  color: #000;

  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  p,
  div,
  span,
  ul,
  li {
    margin-bottom: 0;
  }

  .display-3 {
    @media (max-width: 576px) {
      font-size: 2.5rem;
    }
  }

  .opacity-7 {
    opacity: 0.7;
  }

  .text-blue {
    color: #2a80ff;
  }
}</code></pre>

        <h3 id="component-11-2">11.2 视频黑边修复</h3>
        <p>通过 <code>object-fit</code> 和其他属性，确保视频元素铺满容器，并修复在某些浏览器上可能出现的黑边问题。</p>
        <pre><code class="language-scss">// 去除视频的黑边
video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  // google去黑线
  filter: grayscale(0);
  // 火狐去黑线
  clip-path: fill-box;
}</code></pre>

        <h3 id="component-11-3">11.3 自定义滚动条</h3>
        <p>为带有 <code>.content-box</code> 类的元素设置自定义的 Webkit 滚动条样式。</p>
        <pre><code class="language-scss">//滚动条样式
.content-box {
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.2);
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);

    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }
}</code></pre>

        <h3 id="component-11-4">11.4 按钮样式 (包含悬停效果)</h3>
        <p>
          定义了通用的 <code>.btn-wrapper</code> 和 <code>.btn</code> 样式，以及一个带有复杂渐变背景、阴影和悬停文字滚动效果的 <code>.btn-download</code> 样式。
        </p>
        <pre><code class="language-scss">// MT通用模版

.btn-wrapper {
  display: flex;
  gap: 1rem;
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 8px;
    align-items: center;
  }
}
.btn {
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  text-transform: capitalize;
  gap: 8px;
  svg {
    max-width: 100%;
    height: 100%;
  }
  @media (max-width: 768px) {
    display: block;
  }
  &.dev-mobile {
    width: 168.75px;
    min-width: unset !important;
  }
}
.btn-download {
  border: 1px solid #ffffff;
  background: linear-gradient(89.57deg, #00c8ff -10.59%, #0084ff 15.01%, #006fff 83.38%, #00c8ff 107.75%);
  box-shadow: 0px 4.5px 6.97px 0px #ffffff6e inset, 0px -6.75px 16.65px 0px #00e5ffd6 inset, 0px 4.5px 13.84px 0px #0059ff40;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  text-transform: capitalize;
  border-radius: 0.875rem;
  color: #fff;
  min-width: 248px;
  overflow: hidden;
  position: relative;

  &:focus,
  &:active {
    color: #fff;
  }
  @media (min-width: 992px) {
    height: 4rem;
  }

  .btn-text-wrap {
    position: relative;
    overflow: hidden;
    color: inherit;
    .btn-hover-text-wrap {
      transition: transform 0.4s ease-in-out;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      color: inherit;
      &.rel {
        position: relative;
        transform: translateY(0);
      }
      &.abs {
        position: absolute;
        top: 120%;
        transform: translateY(0);
        transition-duration: 0.45s;
      }
    }
  }

  @media (any-hover: hover) {
    &:hover {
      color: #fff;
    }

    &:hover .btn-hover-text-wrap.rel {
      color: inherit;
      transform: translateY(-100%);
    }

    &:hover .btn-hover-text-wrap.abs {
      color: inherit;
      transform: translateY(-120%);
    }
  }
}</code></pre>

        <h3 id="component-11-5">11.5 CSS Keyframe 动画</h3>
        <p>定义了水平和垂直方向的无缝滚动（走马灯）动画。</p>
        <pre><code class="language-scss">@keyframes marquee1 {
    0% {
      transform: translateX(0);
    }

    100% {
      transform: translateX(-50%);
    }
  }
  @keyframes marquee2 {
    0% {
      transform: translateX(-50%);
    }

    100% {
      transform: translateX(0%);
    }
  }
  @keyframes marquee1-vertical {
    0% {
      transform: translateY(0);
    }

    100% {
      transform: translateY(-50%);
    }
  }
  @keyframes marquee2-vertical {
    0% {
      transform: translateY(-50%);
    }

    100% {
      transform: translateY(0%);
    }
  }</code></pre>

        <h3 id="component-11-6">11.6 带悬停效果的二维码图标</h3>
        <p>一个二维码图标容器，当鼠标悬停时，会显示一个绝对定位的二维码图片。</p>
        <pre><code class="language-scss">.qr-code-icon-wrapper {
  position: relative;
  z-index: 5;
  @media (max-width: 1280px) {
    display: none;
  }
  .active-icon {
    display: none;
  }
  .qrcode-box {
    width: max-content;
    position: absolute;
    top: -8px;
    max-width: 128px;
    left: 50%;
    transform: translate(-50%, -100%);
    transition: opacity 0.2s ease-in-out;
    opacity: 0;
    pointer-events: none;
  }
  &:hover {
    .active-icon {
      display: inline-block;
    }
    .default-icon {
      display: none;
    }
    .qrcode-box {
      opacity: 1;
    }
  }
}</code></pre>

        <h3 id="component-11-7">11.7 Swiper 分页器样式</h3>
        <p>自定义 Swiper 轮播的分页器（pagination）样式，默认状态为圆形，激活状态为长条形。</p>
        <pre><code class="language-scss">.swiper-pagination {
  bottom: -10px !important;
  .swiper-pagination-bullet {
    width: 10px;
    height: 10px;
    border-radius: 100px;
    background-color: rgba($color: #006dff, $alpha: 0.3);
    opacity: 1;
    &.swiper-pagination-bullet-active {
      width: 40px;
      border-radius: 100px;
      opacity: 1;
      background-color: rgba($color: #006dff, $alpha: 0.7);
    }
  }
}</code></pre>
      </section>

      <hr />

      <section class="component-section" id="component-12">
        <h2>12. 通用交互脚本 (JavaScript) - 模块化拆分</h2>
        <p>这里将完整的 JS 文件拆分为多个独立的功能模块，并附上其原始注释说明。</p>

        <h3 id="component-12-1">12.1 AOS 动画在移动端禁用</h3>
        <p>在指定断点下，通过动态添加 CSS 样式，立即移除 AOS 的动画效果，提升移动端性能。</p>
        <pre><code class="language-javascript">// -------------------------------------------------------------------
// 1. AOS动画在小屏幕上禁用
// -------------------------------------------------------------------
(function disableAOSOnMobile() {
  const mobileBreakpoint = 992; // 设置移动端断点
  if (window.innerWidth <= mobileBreakpoint) {
    const style = document.createElement("style");
    style.textContent = `
        [data-aos] {
          transition-duration: 0s !important;
          transform: none !important;
          opacity: 1 !important;
        }
      `;
    document.head.appendChild(style);
  }
})();</code></pre>

        <h3 id="component-12-2">12.2 Swiper 高度自适应解决方案</h3>
        <p>当 Swiper 启用了<code>autoHeight</code>，但 slide 内部元素高度由 JS 动态改变时，提供手动更新 Swiper 高度的解决方案。</p>
        <pre><code class="language-javascript">// -------------------------------------------------------------------
// 2. Swiper 高度自适应 (autoHeight) 解决方案
// -------------------------------------------------------------------
const autoHeightSwiper = new Swiper("#swiper-container-autoheight", {
  // Swiper容器的选择器
  slidesPerView: 1,
  spaceBetween: 30,
  autoHeight: true, // 关键：开启高度自适应
  allowTouchMove: false, // 根据需要设置是否允许手动滑动
  pagination: {
    el: "#swiper-container-autoheight .swiper-pagination", // 分页器的选择器
    clickable: true,
  },
  on: {
    // 在初始化和切换后轻微延迟更新高度，确保slide内容渲染完毕
    init: function () {
      this.updateAutoHeight(50);
    },
    slideChangeTransitionEnd: function () {
      this.updateAutoHeight(50);
    },
  },
});

// 当点击某个元素触发展开/折叠时，手动调用高度更新
$(".your-toggle-element").on("click", function () {
  // 你的展开/折叠逻辑，例如：
  // $(".content-to-expand").slideToggle(200);

  // 在动画结束后更新Swiper高度
  setTimeout(function () {
    if (autoHeightSwiper && autoHeightSwiper.updateAutoHeight) {
      autoHeightSwiper.updateAutoHeight();
    }
  }, 210); // 延迟时间略长于你的动画时间
});

// 页面所有资源加载完后，再次更新高度，防止图片等资源影响计算
$(window).on("load", function () {
  if (autoHeightSwiper && autoHeightSwiper.updateAutoHeight) {
    autoHeightSwiper.updateAutoHeight();
  }
});</code></pre>

        <h3 id="component-12-3">12.3 Swiper 纵向文字轮播</h3>
        <p>用于公告、新闻标题等垂直滚动的文字展示。</p>
        <pre><code class="language-javascript">// -------------------------------------------------------------------
// 3. Swiper 纵向文字轮播
// -------------------------------------------------------------------
const verticalTextSwiper = new Swiper("#vertical-text-swiper", {
  direction: "vertical",
  slidesPerView: 1,
  spaceBetween: 10,
  loop: true,
  allowTouchMove: false,
  autoplay: {
    delay: 2500,
    disableOnInteraction: false,
  },
});</code></pre>

        <h3 id="component-12-4">12.4 Bootstrap 标签页自动切换</h3>
        <p>在 PC 端自动循环播放 Bootstrap 的 Tab 标签页，并在用户手动点击时重置计时器。</p>
        <pre><code class="language-javascript">// -------------------------------------------------------------------
// 4. Bootstrap 标签页自动切换 (仅PC端)
// -------------------------------------------------------------------
(function autoSwitchBootstrapTabs() {
  const desktopBreakpoint = 992;
  if (window.innerWidth <= desktopBreakpoint) return;

  // 重要：将你的tab trigger（通常是.nav-item或.nav-link）的ID填入这里
  const tabs = ["#step-1-tab", "#step-2-tab", "#step-3-tab"];
  const switchInterval = 3000; // 切换间隔(ms)
  let currentIndex = 0;
  let autoSwitchTimer;

  const switchTab = () => {
    currentIndex = (currentIndex + 1) % tabs.length;
    $(tabs[currentIndex]).tab("show");
  };

  autoSwitchTimer = setInterval(switchTab, switchInterval);

  // 用户手动点击时，重置计时器 (已恢复您的原始逻辑)
  // 此处假设被点击的 .nav-item 元素自身拥有ID
  $(".your-tab-nav-container .nav-item").on("click", function () {
    clearInterval(autoSwitchTimer);

    // 使用被点击元素自身的ID来查找其在数组中的位置
    const clickedTabId = $(this).attr("id");
    currentIndex = Math.max(0, tabs.indexOf("#" + clickedTabId)); // 更新当前索引

    autoSwitchTimer = setInterval(switchTab, switchInterval);
  });
})();</code></pre>

        <h3 id="component-12-5">12.5 移动端专用 Swiper</h3>
        <p>某个模块在 PC 端是静态布局，在移动端转为轮播。</p>
        <pre><code class="language-javascript">// -------------------------------------------------------------------
// 5. 移动端专用 Swiper
// -------------------------------------------------------------------
(function initMobileSwiper() {
  const mobileBreakpoint = 992;
  if (window.innerWidth >= mobileBreakpoint) return;

  const mobileSwiper = new Swiper("#mobile-only-swiper", {
    slidesPerView: 1.1, // 可根据设计微调，实现"露出半个"的效果
    spaceBetween: 15,
    centeredSlides: true,
    loop: true,
    autoplay: {
      delay: 3000,
      disableOnInteraction: false,
    },
    pagination: {
      el: "#mobile-only-swiper .swiper-pagination",
      clickable: true,
    },
    breakpoints: {
      // 在移动端内部也可以设置断点
      768: {
        slidesPerView: 2,
        centeredSlides: false,
      },
    },
  });
})();</code></pre>

        <h3 id="component-12-6">12.6 Swiper 淡入淡出效果</h3>
        <p>适用于全屏 Banner 等需要平滑过渡的场景，通常禁止手动滑动。</p>
        <pre><code class="language-javascript">// -------------------------------------------------------------------
// 6. Swiper 淡入淡出效果
// -------------------------------------------------------------------
const fadeEffectSwiper = new Swiper("#fade-effect-swiper", {
  effect: "fade",
  fadeEffect: {
    crossFade: true, // 启用交叉淡入淡出
  },
  loop: true,
  autoplay: {
    delay: 3000,
    disableOnInteraction: false,
  },
  // 禁止所有手动交互
  allowTouchMove: false,
  noSwiping: true,
  simulateTouch: false,
  keyboard: { enabled: false },
});</code></pre>

        <h3 id="component-12-7">12.7 视频懒加载</h3>
        <p>当视频元素进入视口时，才将其 <code>data-src</code> 赋予 <code>src</code> 属性开始加载。</p>
        <pre><code class="language-javascript">// -------------------------------------------------------------------
// 7. 视频懒加载
// -------------------------------------------------------------------
(function videoLazyLoad() {
  let lazyVideos = $(".lazy-video").toArray();
  if (!lazyVideos.length) return;

  const isElementInViewport = (el) => {
    const rect = el.getBoundingClientRect();
    return rect.top >= 0 && rect.bottom <= (window.innerHeight || document.documentElement.clientHeight);
  };

  const lazyLoad = () => {
    lazyVideos = lazyVideos.filter((videoEl) => {
      if (isElementInViewport(videoEl)) {
        const $video = $(videoEl);
        $video.attr("src", $video.data("src"));
        if ($video.data("poster")) {
          $video.attr("poster", $video.data("poster"));
        }
        $video[0].load(); // 开始加载视频
        return false; // 从数组中移除
      }
      return true; // 保留在数组中
    });
    if (!lazyVideos.length) {
      $(window).off("scroll", lazyLoad); // 所有视频加载完后移除监听器
    }
  };

  $(window).on("scroll", lazyLoad);
  $(document).ready(lazyLoad); // 页面加载时检查一次
})();</code></pre>

        <h3 id="component-12-8">12.8 GSAP 滚动堆叠卡片效果</h3>
        <p>使用 GSAP 和 ScrollTrigger 插件创建滚动堆叠卡片效果，仅在 PC 端生效。</p>
        <pre><code class="language-javascript">// -------------------------------------------------------------------
// 8. GSAP 滚动堆叠卡片效果 (仅PC端)
// -------------------------------------------------------------------
(function initScrollStackCards() {
  const desktopBreakpoint = 992;
  if (window.innerWidth <= desktopBreakpoint || typeof gsap === "undefined") return;

  gsap.registerPlugin(ScrollTrigger);

  const cards = gsap.utils.toArray(".scroll-card-item"); // 卡片的通用class
  if (cards.length === 0) return;

  const lastCard = ".last-scroll-card"; // 最后一个卡片的选择器，用作动画结束触发点

  // 确保所有图片加载完毕再执行动画，防止高度计算错误
  const preloadImages = (selector) => {
    return new Promise((resolve) => {
      imagesLoaded(document.querySelectorAll(selector), resolve);
    });
  };

  preloadImages(".scroll-card-item img").then(() => {
    // 在屏幕顶部留出的堆叠高度
    const stackHeight = window.innerHeight * 0.1;

    cards.forEach((card, index) => {
      // 最后一个卡片不应用动画，它作为"底座"
      if (index !== cards.length - 1) {
        gsap.fromTo(
          card,
          { opacity: 1 },
          {
            scale: 0.8, // 缩小到的比例
            opacity: 0.5, // 缩小后的透明度
            ease: "power1.inOut",
            scrollTrigger: {
              trigger: card,
              start: `top ${stackHeight}`, // 动画开始于卡片顶部触碰堆叠区
              end: `top ${stackHeight}`, // 立即触发
              pin: true, // 将卡片固定在屏幕上
              pinSpacing: false, // 不在卡片后面留白
              scrub: 0.5, // 动画与滚动条平滑关联
              endTrigger: lastCard, // 动画的结束点
              invalidateOnRefresh: true, // 窗口尺寸变化时重新计算
            },
          }
        );
      }
    });
  });
})();</code></pre>

        <h3 id="component-12-9">12.9 窗口尺寸变化时的重载与刷新逻辑</h3>
        <p>包含两部分逻辑：1. 窗口变化时刷新 <code>ScrollTrigger</code>；2. 跨越指定断点时重载页面。</p>
        <pre><code class="language-javascript">// -------------------------------------------------------------------
// 9. 窗口尺寸变化时的重载与刷新逻辑
// -------------------------------------------------------------------
(function handleWindowResize() {
  // a. 刷新ScrollTrigger
  const refreshScrollTrigger = () => {
    if (typeof ScrollTrigger !== "undefined") {
      ScrollTrigger.refresh();
    }
  };
  if (window.ResizeObserver) {
    const resizeObserver = new ResizeObserver(debounce(refreshScrollTrigger, 200));
    resizeObserver.observe(document.body);
  } else {
    $(window).on("resize", debounce(refreshScrollTrigger, 200));
  }

  // b. 跨断点时重载页面
  const desktopBreakpoint = 1280;
  let isDesktop = window.innerWidth >= desktopBreakpoint;
  const reloadOnBreakpointChange = () => {
    const newIsDesktop = window.innerWidth >= desktopBreakpoint;
    if (isDesktop !== newIsDesktop) {
      window.location.reload();
    }
  };
  $(window).on("resize", throttle(reloadOnBreakpointChange, 200));
})();</code></pre>

        <h3 id="component-12-10">12.10 叠层表格行高同步</h3>
        <p>当一个表格（前景）叠在另一个表格（背景）上时，同步它们的行高。</p>
        <pre><code class="language-javascript">// -------------------------------------------------------------------
// 10. 叠层表格行高同步
// -------------------------------------------------------------------
(function syncTableRowsHeight() {
  const tableA_selector = ".foreground-table"; // 前景表格
  const tableB_selector = ".background-table"; // 背景表格

  const syncHeight = () => {
    const $tableA = $(tableA_selector);
    const $tableB = $(tableB_selector);
    if (!$tableA.length || !$tableB.length) return;

    const $rowsA = $tableA.find("tr");
    const $rowsB = $tableB.find("tr");
    if ($rowsA.length !== $rowsB.length) return;

    $rowsB.css("height", "auto"); // 先重置背景表格行高
    $rowsA.each(function (index) {
      const rowHeight = $(this).outerHeight();
      $rowsB.eq(index).css("height", `${rowHeight}px`);
    });
  };

  syncHeight();
  $(window).on("load", syncHeight);
  $(window).on("resize", debounce(syncHeight, 100));
})();</code></pre>

        <h3 id="component-12-11">12.11 辅助函数 (Helpers)</h3>
        <p>包含节流（<code>throttle</code>）和防抖（<code>debounce</code>）两个常用的性能优化函数。</p>
        <pre><code class="language-javascript">// -------------------------------------------------------------------
// 辅助函数 (Helpers)
// -------------------------------------------------------------------

/**
 * 节流函数 (Throttle)
 */
function throttle(func, delay) {
  let timeout = null;
  let lastArgs = null;
  let lastThis = null;
  return function (...args) {
    lastArgs = args;
    lastThis = this;
    if (!timeout) {
      timeout = setTimeout(() => {
        func.apply(lastThis, lastArgs);
        timeout = null;
      }, delay);
    }
  };
}

/**
 * 防抖函数 (Debounce)
 */
function debounce(func, delay) {
  let timeout;
  return function (...args) {
    const context = this;
    clearTimeout(timeout);
    timeout = setTimeout(() => func.apply(context, args), delay);
  };
}</code></pre>
      </section>
            <!--
        ================================================================
        下面是新添加的组件部分
        位置：紧跟在 id="component-11" 的 </section> 之后
        ================================================================
      -->
      <section class="component-section" id="component-13">
        <h2>13. 编辑器代码片段 (JSON 源文件)</h2>
        <p>
          以下是完整的编辑器代码片段 (Snippets) 的 JSON 配置文件。您可以直接复制此内容，并将其导入到支持该格式的编辑器（如 VS Code）中。
        </p>
        <h4>Snippets JSON</h4>
        <pre><code class="language-json">{
  "console.log": {
    "scope": "javascript,typescript",
    "prefix": "log",
    "body": ["console.log('$1');", "$2"],
    "description": "输出日志到控制台"
  },
  "respond-xxl": {
    "prefix": "mxxxl",
    "body": ["@media (max-width: 1600px) {", "  ${1}", "}"],
    "description": "超大屏幕响应式断点"
  },
  "respond-xl": {
    "prefix": "mxl",
    "body": ["@media (max-width: 1280px) {", "  ${1}", "}"],
    "description": "大屏幕响应式断点"
  },
  "respond-lg": {
    "prefix": "mlg",
    "body": ["@media (max-width: 992px) {", "  ${1}", "}"],
    "description": "中等屏幕响应式断点"
  },
  "respond-md": {
    "prefix": "mmd",
    "body": ["@media (max-width: 768px) {", "  ${1}", "}"],
    "description": "平板响应式断点"
  },
  "respond-sm": {
    "prefix": "msm",
    "body": ["@media (max-width: 576px) {", "  ${1}", "}"],
    "description": "手机响应式断点"
  },
  "1. Media Query (min-width)": {
    "prefix": "@media-min",
    "body": ["@media (min-width: ${1:768}px) {", "  $0", "}"],
    "description": "创建一个 min-width 的媒体查询"
  },
  "2. Media Query (max-width)": {
    "prefix": "@media-max",
    "body": ["@media (max-width: ${1:768}px) {", "  $0", "}"],
    "description": "创建一个 max-width 的媒体查询"
  },
  "3. Flexbox Center": {
    "prefix": "flex-center",
    "body": ["display: flex;", "align-items: center;", "justify-content: center;"],
    "description": "使用 Flexbox 将元素完全居中"
  },
  "4. Position Absolute Center": {
    "prefix": "pos-center",
    "body": ["position: absolute;", "top: 50%;", "left: 50%;", "transform: translate(-50%, -50%);"],
    "description": "使用绝对定位将元素完全居中"
  },
  "5. Hover Transition": {
    "prefix": "hover-trans",
    "body": ["transition: all 0.3s ease-in-out;", "", "&:hover {", "  $0", "}"],
    "description": "添加一个平滑的 hover 过渡效果"
  },
  "respond-hover": {
    "prefix": "ah",
    "body": [" @media (any-hover: hover) {", "${1}", " }"],
    "description": "悬停设备响应式断点"
  },
  "Page Section": {
    "prefix": "part-section",
    "body": [
      "<section class=\"part-$1 py-5\">",
      "  <div class=\"container my-xl-5 my-lg-3\">",
      "    <h2 class=\"mb-3\">$2</h2>",
      "    <p class=\"font-size-large text-center\">$3</p>",
      "    <div class=\"row py-4 justify-content-center\">",
      "      $0",
      "    </div>",
      "  </div>",
      "</section>"
    ],
    "description": "创建一个新的页面区块"
  },
  "HTML5 Video Player": {
    "prefix": "video-player",
    "body": [
      "<div class=\"video-wrapper\">",
      "  <video src=\"${1:path/to/video.mp4}\" poster=\"${2:path/to/poster.jpg}\" autoplay muted loop playsinline></video>",
      "</div>"
    ],
    "description": "创建一个自动播放、静音、循环的H5视频播放器"
  },
  "Swiper Carousel": {
    "prefix": "ws-swiper",
    "body": [
      "<div class=\"swiper\" id=\"${1:swiper-id}\">",
      "  <div class=\"swiper-wrapper\">",
      "    <div class=\"swiper-slide\">${2:Slide 1}</div>",
      "    <div class=\"swiper-slide\">${3:Slide 2}</div>",
      "    <div class=\"swiper-slide\">${4:Slide 3}</div>",
      "  </div>",
      "  <div class=\"swiper-pagination\"></div>",
      "  <div class=\"swiper-button-prev\"></div>",
      "  <div class=\"swiper-button-next\"></div>",
      "</div>"
    ],
    "description": "创建一个完整的 Swiper 轮播结构"
  },
  "jQuery Document Ready": {
    "prefix": "jq-ready",
    "body": ["$(() => {", "  $1", "});"],
    "description": "jQuery $(document).ready() 简写"
  },
  "Swiper Initialization": {
    "prefix": "jq-swiper",
    "body": [
      "const ${1:swiper} = new Swiper('#${2:swiper-id}', {",
      "  loop: true,",
      "  autoplay: {",
      "    delay: 3000,",
      "    disableOnInteraction: false,",
      "  },",
      "  pagination: {",
      "    el: '.swiper-pagination',",
      "    clickable: true,",
      "  },",
      "  navigation: {",
      "    nextEl: '.swiper-button-next',",
      "    prevEl: '.swiper-button-prev',",
      "  },",
      "  breakpoints: {",
      "    768: {",
      "      slidesPerView: 2,",
      "      spaceBetween: 20",
      "    },",
      "    992: {",
      "      slidesPerView: 3,",
      "      spaceBetween: 30",
      "    }",
      "  }",
      "});"
    ],
    "description": "初始化一个带响应式参数的 Swiper 实例"
  },
  "jQuery Click Handler": {
    "prefix": "jq-click",
    "body": ["$('${1:selector}').on('click', function() {", "  $2", "});"],
    "description": "jQuery click 事件处理器"
  },
  "Check Window Width": {
    "prefix": "jq-width",
    "body": ["if (window.innerWidth < ${1:768}) {", "  $2", "}"],
    "description": "检查窗口宽度以执行移动端逻辑"
  }
}</code></pre>
      </section>
      <section class="component-section" id="component-14">
        <h2>14. 通用卡片滑块/悬停组件</h2>
        <p>一个响应式组件，在桌面端（1280px以上）通过鼠标悬停来展开卡片，在移动端则自动转换为可滑动的 Swiper 轮播。通过 SCSS 变量 <code>--card-count</code> 可以方便地调整桌面端的卡片数量。</p>

        <h4>HTML</h4>
        <pre><code class="language-html">
          &lt;div class=&quot;card-slider-container swiper pt-xl-3 py-5 overflow-hidden position-relative&quot; id=&quot;my-card-slider&quot;&gt;
          &lt;!-- Swiper Wrapper --&gt;
          &lt;div class=&quot;swiper-wrapper&quot;&gt;
            &lt;!-- 卡片 1 --&gt;
            &lt;div class=&quot;swiper-slide h-auto&quot;&gt;
              &lt;div class=&quot;card-slider-item&quot; style=&quot;background-image: url(&#39;https://images.wondershare.com/recoverit/images2025/GoPro/surf.jpg&#39;)&quot;&gt;
                &lt;div class=&quot;card-content-wrapper&quot;&gt;
                  &lt;h3 class=&quot;card-title&quot;&gt;Surfing&lt;/h3&gt;
                  &lt;p class=&quot;card-description&quot;&gt;A detailed description about the surfing card goes here. It explains the content or service related to this item.&lt;/p&gt;
                &lt;/div&gt;
              &lt;/div&gt;
            &lt;/div&gt;
        
            &lt;!-- 卡片 2 --&gt;
            &lt;div class=&quot;swiper-slide h-auto is-active&quot;&gt;
              &lt;!-- 默认激活项 --&gt;
              &lt;div class=&quot;card-slider-item&quot; style=&quot;background-image: url(&#39;https://images.wondershare.com/recoverit/images2025/GoPro/snowboard.jpg&#39;)&quot;&gt;
                &lt;div class=&quot;card-content-wrapper&quot;&gt;
                  &lt;h3 class=&quot;card-title&quot;&gt;Snowboarding&lt;/h3&gt;
                  &lt;p class=&quot;card-description&quot;&gt;A detailed description about the snowboarding card. It explains the content or service related to this item.&lt;/p&gt;
                &lt;/div&gt;
              &lt;/div&gt;
            &lt;/div&gt;
        
            &lt;!-- 卡片 3 --&gt;
            &lt;div class=&quot;swiper-slide h-auto&quot;&gt;
              &lt;div class=&quot;card-slider-item&quot; style=&quot;background-image: url(&#39;https://images.wondershare.com/recoverit/images2025/GoPro/ski.jpg&#39;)&quot;&gt;
                &lt;div class=&quot;card-content-wrapper&quot;&gt;
                  &lt;h3 class=&quot;card-title&quot;&gt;Skiing&lt;/h3&gt;
                  &lt;p class=&quot;card-description&quot;&gt;A detailed description about the skiing card. It explains the content or service related to this item.&lt;/p&gt;
                &lt;/div&gt;
              &lt;/div&gt;
            &lt;/div&gt;
        
            &lt;!-- 卡片 4 --&gt;
            &lt;div class=&quot;swiper-slide h-auto&quot;&gt;
              &lt;div class=&quot;card-slider-item&quot; style=&quot;background-image: url(&#39;https://images.wondershare.com/recoverit/images2025/GoPro/bike.jpg&#39;)&quot;&gt;
                &lt;div class=&quot;card-content-wrapper&quot;&gt;
                  &lt;h3 class=&quot;card-title&quot;&gt;Biking&lt;/h3&gt;
                  &lt;p class=&quot;card-description&quot;&gt;A detailed description about the biking card. It explains the content or service related to this item.&lt;/p&gt;
                &lt;/div&gt;
              &lt;/div&gt;
            &lt;/div&gt;
            
            &lt;!-- ...可以添加更多卡片 --&gt;
          &lt;/div&gt;
          &lt;!-- Swiper Pagination --&gt;
          &lt;div class=&quot;swiper-pagination&quot;&gt;&lt;/div&gt;
        &lt;/div&gt;</code></pre>

        <h4>SCSS</h4>
        <pre><code class="language-scss">/* 
  通用卡片滑块/悬停组件样式
*/
.card-slider-container {
  // --- 可配置变量 ---
  --card-count: 4; // 【重要】桌面端卡片总数
  --gap: 0.5rem; // 卡片间距
  --active-width: 37.5%; // 展开卡片的宽度
  // -----------------

  .swiper-wrapper {
    // 移动端默认为 Swiper 的 flex 布局
    // 桌面端自定义布局
    @media (min-width: 1280px) {
      display: flex;
      gap: var(--gap);
      aspect-ratio: 1410 / 640; // 容器宽高比，可根据需要调整
      justify-content: center;
    }
  }

  .swiper-slide {
    transition: width 0.4s ease-in-out;
    height: 100%;
    overflow: hidden;

    @media (min-width: 1280px) {
      // 使用 calc() 动态计算未展开卡片的宽度
      // (总宽度 100% - 激活卡片宽度 - 所有间距) / (卡片总数 - 1)
      --inactive-width: calc((100% - var(--active-width) - (var(--card-count) - 1) * var(--gap)) / (var(--card-count) - 1));
      width: var(--inactive-width);

      &.is-active {
        width: var(--active-width);

        .card-content-wrapper {
          padding: 2rem;
        }
        .card-description {
          display: block;
          opacity: 0.7;
        }
      }
    }
  }

  // Swiper 分页器样式
  .swiper-pagination-bullet {
    background-color: #fff;
    opacity: 0.5;
    &.swiper-pagination-bullet-active {
      background-color: #007aff;
      opacity: 1;
    }
  }
}

.card-slider-item {
  width: 100%;
  height: 100%;
  position: relative;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  border-radius: 1rem;
  overflow: hidden;
  color: #fff;

  // 移动端卡片宽高比
  @media (max-width: 1279.98px) {
    aspect-ratio: 530 / 640;
  }

  // 底部渐变遮罩，增强文字可读性
  &::after {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.8) 60%);
    background-size: 100% 50%;
    background-position: center bottom;
    background-repeat: no-repeat;
  }
}

.card-content-wrapper {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 2;
  padding: 2rem 0.75rem;
  box-sizing: border-box;

  .card-title {
    font-weight: 800;
    font-size: 1.5rem;
    margin-top: 0;
    margin-bottom: 1rem;
    @media (max-width: 1600px) {
      font-size: 1.25rem;
    }
  }

  .card-description {
    font-size: 0.875rem;
    margin: 0;
    // 桌面端默认隐藏
    @media (min-width: 1280px) {
      display: none;
    }
    // 移动端默认显示
    @media (max-width: 1279.98px) {
      display: block;
      opacity: 0.7;
    }
  }
}</code></pre>

        <h4>JavaScript</h4>
        <pre><code class="language-javascript">if (window.innerWidth > 1280) {
  // 大屏幕使用鼠标悬停效果
  $("#my-card-slider .swiper-slide").on("mouseenter", function () {
    $(this).addClass("is-active").siblings().removeClass("is-active");
  });
} else {
  // 小屏幕使用轮播
  return new Swiper("#my-card-slider", {
    loop: true,
    slidesPerView: 1,
    spaceBetween: 10,
    autoplay: {
      delay: 3000,
      disableOnInteraction: false,
    },
    pagination: {
      el: ".swiper-pagination",
      clickable: true,
    },
    breakpoints: {
      768: {
        slidesPerView: 2,
        spaceBetween: 20,
      },
      992: {
        slidesPerView: 3,
        spaceBetween: 15,
      },
    },
  });
}</code></pre>
      </section>
      
    </main>

    <!-- jQuery (required for Bootstrap) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Bootstrap JS for accordion functionality -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Swiper JS -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@7/swiper-bundle.min.js"></script>

    <!-- Prism.js for Syntax Highlighting (JS) -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>

    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // --- 1. 导航链接点击时，在移动端关闭菜单 ---
        const sidebarNav = document.getElementById("sidebar-nav");
        sidebarNav.addEventListener("click", function (event) {
          if (event.target.tagName === "A" && window.innerWidth <= 768) {
            document.body.classList.remove("sidebar-open");
          }
        });

        // --- 2. 移动端菜单开关 ---
        const menuToggle = document.getElementById("menu-toggle");
        menuToggle.addEventListener("click", function () {
          document.body.classList.toggle("sidebar-open");
        });

        // --- 3. 页面滚动时，高亮对应的导航项 (Intersection Observer API) ---
        const sections = document.querySelectorAll('.component-section, h3[id^="component-"]');
        const navLinks = document.querySelectorAll(".sidebar-nav a");

        const observerOptions = {
          root: null,
          rootMargin: "0px",
          threshold: 0.3, // 当区域30%可见时触发
        };

        const observer = new IntersectionObserver((entries, observer) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              const id = entry.target.getAttribute("id");
              navLinks.forEach((link) => {
                link.classList.remove("active");
                if (link.getAttribute("href") === `#${id}`) {
                  link.classList.add("active");
                }
              });
            }
          });
        }, observerOptions);

        sections.forEach((section) => {
          observer.observe(section);
        });

        // --- 4. 代码复制功能 ---
        function initCodeCopy() {
          const codeBlocks = document.querySelectorAll('pre[class*="language-"]');

          codeBlocks.forEach((block) => {
            // 创建复制按钮
            const copyBtn = document.createElement("button");
            copyBtn.className = "copy-btn";
            copyBtn.innerHTML = `
              <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M16 1H4C2.9 1 2 1.9 2 3V17H4V3H16V1ZM19 5H8C6.9 5 6 5.9 6 7V21C6 22.1 6.9 23 8 23H19C20.1 23 21 22.1 21 21V7C21 5.9 20.1 5 19 5ZM19 21H8V7H19V21Z"/>
              </svg>
              <div class="copy-tooltip">已复制!</div>
            `;

            // 将按钮添加到代码块中
            block.appendChild(copyBtn);

            // 添加点击事件
            copyBtn.addEventListener("click", async function (e) {
              e.preventDefault();
              e.stopPropagation();

              const codeElement = block.querySelector("code");
              const textToCopy = codeElement ? codeElement.textContent : block.textContent;

              try {
                await navigator.clipboard.writeText(textToCopy);

                // 显示复制成功状态
                copyBtn.classList.add("copied");
                const tooltip = copyBtn.querySelector(".copy-tooltip");
                tooltip.classList.add("show");

                // 2秒后恢复原始状态
                setTimeout(() => {
                  copyBtn.classList.remove("copied");
                  tooltip.classList.remove("show");
                }, 2000);
              } catch (err) {
                // 降级方案：使用传统的复制方法
                const textArea = document.createElement("textarea");
                textArea.value = textToCopy;
                textArea.style.position = "fixed";
                textArea.style.left = "-999999px";
                textArea.style.top = "-999999px";
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();

                try {
                  document.execCommand("copy");

                  // 显示复制成功状态
                  copyBtn.classList.add("copied");
                  const tooltip = copyBtn.querySelector(".copy-tooltip");
                  tooltip.classList.add("show");

                  // 2秒后恢复原始状态
                  setTimeout(() => {
                    copyBtn.classList.remove("copied");
                    tooltip.classList.remove("show");
                  }, 2000);
                } catch (fallbackErr) {
                  console.error("复制失败:", fallbackErr);
                  // 可以在这里添加错误提示
                } finally {
                  document.body.removeChild(textArea);
                }
              }
            });
          });
        }

        // 初始化代码复制功能
        initCodeCopy();

        // --- 5. 初始化手风琴轮播组件 ---
        function initFeatureAccordionSwiper() {
          // 检查是否存在必要的元素
          const swiperElement = document.getElementById("feature-swiper");
          const accordionElement = document.getElementById("feature-accordion");

          if (!swiperElement || !accordionElement) {
            return; // 如果元素不存在，直接返回
          }

          // 检查是否已加载 Swiper 库
          if (typeof Swiper === 'undefined') {
            console.warn('Swiper library not loaded. Please include Swiper.js to use the accordion swiper component.');
            return;
          }

          // 初始化 Swiper
          const featureSwiper = new Swiper("#feature-swiper", {
            effect: "fade",
            fadeEffect: {
              crossFade: true,
            },
            // 禁用所有用户交互
            allowTouchMove: false,
            noSwiping: true,
            simulateTouch: false,
            keyboard: {
              enabled: false
            },
          });

          // 绑定手风琴点击事件到轮播切换
          const accordionHeaders = document.querySelectorAll("#feature-accordion .feature-accordion-header");
          accordionHeaders.forEach(function (header, index) {
            header.addEventListener("click", function () {
              // 当手风琴头部被点击时，切换到对应的轮播项
              featureSwiper.slideTo(index);
            });
          });
        }

        // 初始化手风琴轮播组件
        initFeatureAccordionSwiper();
      });
    </script>
  </body>
</html>
