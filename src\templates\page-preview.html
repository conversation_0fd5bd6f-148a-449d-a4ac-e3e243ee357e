<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <meta name="description" content="" />
    <title>预览页面 - 开发预览</title>

    <!-- 可爱的预览页面Favicon 👀 -->
    <link rel="icon" type="image/svg+xml" href="/assets/favicons/eye-emoji.svg" />
    <link rel="shortcut icon" type="image/x-icon" href="/assets/favicons/eye-emoji.svg" />
    <link rel="apple-touch-icon" href="/assets/favicons/eye-emoji.svg" />

    <!-- 基础样式 -->
    <link rel="stylesheet" href="/shared/scss/_variables.scss" />
    <link rel="stylesheet" href="/shared/scss/_mixins.scss" />
    <link rel="stylesheet" href="/shared/scss/_base.scss" />
    <link rel="stylesheet" href="/shared/scss/_header.scss" />
    <link rel="stylesheet" href="/shared/scss/_footer.scss" />

    <!-- 页面专用样式 - 动态加载 -->
    <link rel="stylesheet" href="" id="page-style" />

    <style>
      /* 开发预览提示条 */
      .dev-preview-bar {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        background: linear-gradient(90deg, #667eea, #764ba2);
        color: white;
        padding: 8px 20px;
        font-size: 14px;
        z-index: 9999;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .dev-preview-bar .info {
        display: flex;
        align-items: center;
        gap: 15px;
      }

      .dev-preview-bar .badge {
        background: rgba(255, 255, 255, 0.2);
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
      }

      .dev-preview-bar .actions {
        display: flex;
        gap: 10px;
      }

      .dev-preview-bar .btn-small {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: none;
        padding: 6px 12px;
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .dev-preview-bar .btn-small:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-1px);
      }

      /* 为开发预览条腾出空间 */
      body {
        padding-top: 50px;
      }

      /* 加载状态样式 */
      .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(102, 126, 234, 0.9);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9998;
        opacity: 1;
        transition: opacity 0.3s ease;
      }

      .loading-overlay.fade-out {
        opacity: 0;
        pointer-events: none;
      }

      .loading-content {
        text-align: center;
        color: white;
      }

      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      @media (max-width: 768px) {
        .dev-preview-bar {
          flex-direction: column;
          gap: 10px;
          padding: 10px 15px;
        }

        .dev-preview-bar .info {
          flex-wrap: wrap;
          justify-content: center;
        }

        .dev-preview-bar .actions {
          flex-wrap: wrap;
          justify-content: center;
        }

        body {
          padding-top: 80px;
        }
      }
    </style>
  </head>
  <body>
    <!-- 加载状态覆盖层 -->
    <div class="loading-overlay" id="loading-overlay">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <h3>🚀 正在加载预览页面...</h3>
        <p>配置加载中，请稍候</p>
      </div>
    </div>

    <!-- 开发预览提示条 -->
    <div class="dev-preview-bar">
      <div class="info">
        <span>🚀 开发预览模式</span>
        <span class="badge" id="page-badge">加载中...</span>
        <span id="page-title">正在加载...</span>
      </div>
      <div class="actions">
        <button class="btn-small" onclick="editMain()">📝 编辑Main</button>
        <button class="btn-small" onclick="editStyle()">🎨 编辑样式</button>
        <button class="btn-small" onclick="editScript()">⚡ 编辑脚本</button>
        <button class="btn-small" onclick="buildPage()">🔨 构建</button>
      </div>
    </div>

    <!-- 引入公共头部 -->
    <div id="header-placeholder"></div>

    <!-- 页面主要内容 -->
    <div id="main-placeholder"></div>

    <!-- 引入公共底部 -->
    <div id="footer-placeholder"></div>

    <!-- 公共脚本 -->
    <script type="module" src="/shared/js/common.js"></script>

    <!-- 页面专用脚本 - 动态加载 -->
    <script type="module" src="" id="page-script"></script>

    <!-- 预览生成器 -->
    <script type="module" src="/preview-generator.js"></script>

    <script>
      // 扩展预览生成器，添加加载状态管理
      document.addEventListener("DOMContentLoaded", () => {
        const loadingOverlay = document.getElementById("loading-overlay");
        const pageStyle = document.getElementById("page-style");
        const pageScript = document.getElementById("page-script");

        // 监听预览生成器初始化完成
        const originalInit = window.previewGenerator.init;
        window.previewGenerator.init = async function () {
          try {
            // 设置页面专用样式和脚本路径
            if (this.currentPageId) {
              pageStyle.href = `/pages/${this.currentPageId}/style.scss`;
              pageScript.src = `/pages/${this.currentPageId}/script.js`;
            }

            // 调用原始初始化方法
            await originalInit.call(this);

            // 初始化完成，隐藏加载覆盖层
            setTimeout(() => {
              loadingOverlay.classList.add("fade-out");
              setTimeout(() => {
                loadingOverlay.style.display = "none";
              }, 300);
            }, 500);
          } catch (error) {
            // 初始化失败，也要隐藏加载覆盖层
            loadingOverlay.classList.add("fade-out");
            setTimeout(() => {
              loadingOverlay.style.display = "none";
            }, 300);
            throw error;
          }
        };
      });
    </script>
  </body>
</html>
