// // 颜色变量
// $primary-color: #7a57ee;
// $secondary-color: #39a0fa;
// $accent-color: #24e3c8;
// $text-color: #333;
// $light-text-color: #666;
// $bg-color: #fff;
// $light-bg-color: #f8f9fa;

// // 尺寸变量
// $container-max-width: 1200px;
// $border-radius: 8px;
// $large-border-radius: 15px;
// $box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
// $large-box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);

// // 字体变量
// $font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
// $font-size-base: 1rem;
// $font-size-lg: 1.2rem;
// $font-size-xl: 1.5rem;
// $font-size-xxl: 2rem;
// $font-size-xxxl: 2.5rem;

// // 间距变量
// $spacing-xs: 0.5rem;
// $spacing-sm: 1rem;
// $spacing-md: 1.5rem;
// $spacing-lg: 2rem;
// $spacing-xl: 3rem;
// $spacing-xxl: 4rem;

// // 响应式断点
// $breakpoints: (
//   "mobile": 768px,
//   "tablet": 1024px,
//   "desktop": 1200px,
// );

// // 动画变量
// $transition-base: all 0.3s ease;
// $transition-fast: all 0.15s ease;
// $transition-slow: all 0.5s ease;
