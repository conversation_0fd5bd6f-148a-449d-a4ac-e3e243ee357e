import { defineConfig } from "vite";
import { resolve } from "path";

// 注释：热更新现在使用轮询机制，不再需要WebSocket插件

export default defineConfig({
  root: "src",
  server: {
    host: "0.0.0.0", // 允许通过本地IP访问
    port: 3000,
    open: true,
    // 配置静态资源服务
    fs: {
      // 允许访问项目根目录之外的文件
      allow: [".."],
    },
  },
  build: {
    outDir: "../dist",
    rollupOptions: {
      input: {
        // 开发时的入口点
        dev: resolve(__dirname, "src/index.html"),
      },
    },
  },
  css: {
    preprocessorOptions: {
      scss: {
        // 移除自动导入，让文件自己管理导入
        charset: false, // 使用UTF-8编码
      },
    },
  },
  // 配置静态资源处理
  assetsInclude: ["**/*.pdf", "**/*.doc", "**/*.docx", "**/*.zip", "**/*.rar"],

  // 注释：不再需要自定义热更新插件，使用轮询机制
  // plugins: [],
});
